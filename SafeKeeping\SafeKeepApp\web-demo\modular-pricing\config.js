/**
 * Configuration for Modular Pricing UI
 * Contains service definitions, pricing plans, and UI settings
 */

/**
 * Service configuration with features and pricing
 * @type {Object.<string, ServiceOption>}
 */
const SERVICE_CONFIG = {
    contacts: {
        id: 'contacts',
        name: 'Contacts Backup',
        description: 'Secure backup of your contact list',
        features: [
            'Complete contact information',
            'Phone numbers and emails',
            'Contact photos and notes',
            'Automatic sync',
            'Cross-device restore'
        ],
        icon: '👥',
        individualPrice: 99, // $0.99 in cents
        storageGB: 1
    },
    messages: {
        id: 'messages',
        name: 'Messages Backup',
        description: 'Backup your text messages and conversations',
        features: [
            'SMS and MMS messages',
            'Message attachments',
            'Conversation history',
            'Search and filter',
            'Export capabilities'
        ],
        icon: '💬',
        individualPrice: 199, // $1.99 in cents
        storageGB: 1
    },
    photos: {
        id: 'photos',
        name: 'Photos Backup',
        description: 'Secure backup of your photo library',
        features: [
            'High-resolution photos',
            'Video backup',
            'Album organization',
            'Metadata preservation',
            'Bulk download'
        ],
        icon: '📸',
        individualPrice: 499, // $4.99 in cents
        storageGB: 10
    }
};

/**
 * Pricing plans configuration
 * @type {Object}
 */
const PRICING_PLANS = {
    individual: {
        contacts: { price: 99, name: 'Contacts Only', storageGB: 1 },
        messages: { price: 199, name: 'Messages Only', storageGB: 1 },
        photos: { price: 499, name: 'Photos Only', storageGB: 10 }
    },
    combinations: {
        contactsMessages: { 
            price: 249, 
            name: 'Contacts + Messages', 
            savings: 49,
            storageGB: 2,
            services: ['contacts', 'messages']
        },
        contactsPhotos: { 
            price: 549, 
            name: 'Contacts + Photos', 
            savings: 49,
            storageGB: 11,
            services: ['contacts', 'photos']
        },
        messagesPhotos: { 
            price: 649, 
            name: 'Messages + Photos', 
            savings: 49,
            storageGB: 11,
            services: ['messages', 'photos']
        },
        complete: { 
            price: 699, 
            name: 'Complete Backup', 
            savings: 99, 
            isPopular: true,
            badge: 'Most Popular',
            storageGB: 12,
            services: ['contacts', 'messages', 'photos']
        }
    }
};

/**
 * UI configuration settings
 */
const UI_CONFIG = {
    currency: 'USD',
    currencySymbol: '$',
    animationDuration: 300,
    debounceDelay: 500,
    mobileBreakpoint: 768,
    tabletBreakpoint: 1024
};

/**
 * API endpoints configuration
 */
const API_CONFIG = {
    baseUrl: '/api',
    endpoints: {
        pricing: '/pricing/calculate',
        combinations: '/pricing/combinations',
        validate: '/services/validate'
    }
};

// Export configurations
if (typeof window !== 'undefined') {
    window.ModularPricingConfig = {
        SERVICE_CONFIG,
        PRICING_PLANS,
        UI_CONFIG,
        API_CONFIG
    };
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SERVICE_CONFIG,
        PRICING_PLANS,
        UI_CONFIG,
        API_CONFIG
    };
}
