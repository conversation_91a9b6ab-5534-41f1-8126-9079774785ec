import { Request, Response, NextFunction } from 'express';
import { PricingEngine } from '../services/PricingEngine';
import { ApiResponse } from '../types/modular-pricing';

/**
 * Middleware to validate service combinations in requests
 */
export class ServiceValidationMiddleware {
  private pricingEngine: PricingEngine;

  constructor() {
    this.pricingEngine = new PricingEngine();
  }

  /**
   * Validate service IDs in request body
   */
  validateServiceIds = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { serviceIds } = req.body;

      // Check if serviceIds is provided and is an array
      if (!serviceIds || !Array.isArray(serviceIds)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'serviceIds must be provided as an array',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Check if array is not empty
      if (serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'At least one service ID must be provided',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Check if all elements are strings
      if (!serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service combination
      const validation = await this.pricingEngine.validateServiceCombination(serviceIds);
      if (!validation.isValid) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_SERVICE_COMBINATION',
            message: validation.errors.join(', '),
            details: {
              invalidServices: serviceIds.filter(id => !validation.validServices.includes(id)),
              validServices: validation.validServices
            },
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Add validated services to request for downstream use
      req.body.validatedServiceIds = validation.validServices;
      next();
    } catch (error) {
      console.error('Service validation middleware error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'SERVICE_VALIDATION_ERROR',
          message: 'Failed to validate service combination',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  };

  /**
   * Validate user has access to required services
   */
  validateServiceAccess = (requiredServices: string[]) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const userId = req.params.userId || req.body.userId || (req as any).user?.id;

        if (!userId) {
          const response: ApiResponse = {
            success: false,
            error: {
              code: 'AUTHENTICATION_ERROR',
              message: 'User ID not found in request',
              timestamp: new Date().toISOString()
            }
          };
          res.status(401).json(response);
          return;
        }

        // Check access for each required service
        const accessChecks = await Promise.all(
          requiredServices.map(async (serviceId) => {
            try {
              const hasAccess = await this.pricingEngine.validateServiceCombination([serviceId]);
              return { serviceId, hasAccess: hasAccess.isValid };
            } catch (error) {
              return { serviceId, hasAccess: false };
            }
          })
        );

        const deniedServices = accessChecks
          .filter(check => !check.hasAccess)
          .map(check => check.serviceId);

        if (deniedServices.length > 0) {
          const response: ApiResponse = {
            success: false,
            error: {
              code: 'SERVICE_ACCESS_DENIED',
              message: `Access denied for services: ${deniedServices.join(', ')}`,
              details: {
                deniedServices,
                requiredServices
              },
              timestamp: new Date().toISOString()
            }
          };
          res.status(403).json(response);
          return;
        }

        next();
      } catch (error) {
        console.error('Service access validation error:', error);
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'SERVICE_ACCESS_VALIDATION_ERROR',
            message: 'Failed to validate service access',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    };
  };

  /**
   * Validate subscription status and service access
   */
  validateActiveSubscription = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.params.userId || req.body.userId || (req as any).user?.id;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'User ID not found in request',
            timestamp: new Date().toISOString()
          }
        };
        res.status(401).json(response);
        return;
      }

      // This would typically check the user's subscription status
      // For now, we'll assume the user has an active subscription
      // In a real implementation, you'd query the database here

      next();
    } catch (error) {
      console.error('Subscription validation error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'SUBSCRIPTION_VALIDATION_ERROR',
          message: 'Failed to validate subscription status',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  };

  /**
   * Rate limiting for pricing calculations
   */
  rateLimitPricingCalculations = (maxRequests: number = 100, windowMs: number = 60000) => {
    const requestCounts = new Map<string, { count: number; resetTime: number }>();

    return (req: Request, res: Response, next: NextFunction): void => {
      const clientId = req.ip || 'unknown';
      const now = Date.now();
      
      const clientData = requestCounts.get(clientId);
      
      if (!clientData || now > clientData.resetTime) {
        // Reset or initialize counter
        requestCounts.set(clientId, {
          count: 1,
          resetTime: now + windowMs
        });
        next();
        return;
      }

      if (clientData.count >= maxRequests) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: `Too many pricing calculation requests. Limit: ${maxRequests} per ${windowMs/1000} seconds`,
            details: {
              limit: maxRequests,
              windowMs,
              retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
            },
            timestamp: new Date().toISOString()
          }
        };
        res.status(429).json(response);
        return;
      }

      // Increment counter
      clientData.count++;
      next();
    };
  };
}

// Export singleton instance
export const serviceValidation = new ServiceValidationMiddleware();

// Export individual middleware functions for convenience
export const validateServiceIds = serviceValidation.validateServiceIds;
export const validateServiceAccess = serviceValidation.validateServiceAccess;
export const validateActiveSubscription = serviceValidation.validateActiveSubscription;
export const rateLimitPricingCalculations = serviceValidation.rateLimitPricingCalculations;
