import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';
import { serviceAccessService, AccessCheckResult } from '../services/ServiceAccessService';

interface ServiceAccessGuardProps {
  serviceId: 'contacts' | 'messages' | 'photos';
  children: React.ReactNode;
  fallbackComponent?: React.ReactNode;
  showUpgradePrompt?: boolean;
  onAccessDenied?: () => void;
}

const ServiceAccessGuard: React.FC<ServiceAccessGuardProps> = ({
  serviceId,
  children,
  fallbackComponent,
  showUpgradePrompt = true,
  onAccessDenied,
}) => {
  const [accessResult, setAccessResult] = useState<AccessCheckResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    checkAccess();
  }, [serviceId]);

  const checkAccess = async () => {
    setIsLoading(true);
    try {
      const result = await serviceAccessService.hasServiceAccess(serviceId);
      setAccessResult(result);
    } catch (error) {
      console.error('Error checking service access:', error);
      setAccessResult({
        hasAccess: false,
        reason: 'no_subscription',
        upgradeRequired: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = () => {
    navigation.navigate('ModularPricing' as never);
  };

  const handleAccessDenied = () => {
    if (onAccessDenied) {
      onAccessDenied();
    } else if (showUpgradePrompt) {
      serviceAccessService.showUpgradePrompt(serviceId, handleUpgrade);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Checking access...</Text>
      </View>
    );
  }

  if (!accessResult?.hasAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <ServiceAccessDenied
        serviceId={serviceId}
        reason={accessResult?.reason}
        onUpgrade={handleUpgrade}
        onAccessDenied={handleAccessDenied}
      />
    );
  }

  return <>{children}</>;
};

interface ServiceAccessDeniedProps {
  serviceId: 'contacts' | 'messages' | 'photos';
  reason?: string;
  onUpgrade: () => void;
  onAccessDenied: () => void;
}

const ServiceAccessDenied: React.FC<ServiceAccessDeniedProps> = ({
  serviceId,
  reason,
  onUpgrade,
  onAccessDenied,
}) => {
  const serviceInfo = {
    contacts: {
      name: 'Contacts Backup',
      icon: 'account-multiple',
      description: 'Secure backup and sync of your contacts',
      features: ['Contact deduplication', 'Cross-device sync', 'Backup history', 'Export options']
    },
    messages: {
      name: 'Messages Backup',
      icon: 'message-text',
      description: 'Secure backup and sync of your messages',
      features: ['SMS & MMS backup', 'Message threading', 'Search functionality', 'Media attachments']
    },
    photos: {
      name: 'Photos Backup',
      icon: 'camera',
      description: 'Secure backup and sync of your photos',
      features: ['Automatic photo backup', 'Smart compression', 'Album organization', 'Face recognition']
    }
  };

  const service = serviceInfo[serviceId];
  
  const getReasonMessage = () => {
    switch (reason) {
      case 'no_subscription':
        return 'You need a SafeKeep subscription to access this feature.';
      case 'service_not_included':
        return 'This service is not included in your current plan.';
      case 'subscription_expired':
        return 'Your subscription has expired. Renew to continue using this feature.';
      case 'subscription_cancelled':
        return 'Your subscription was cancelled. Reactivate to access this feature.';
      default:
        return 'Premium subscription required for this feature.';
    }
  };

  const getActionText = () => {
    switch (reason) {
      case 'subscription_expired':
      case 'subscription_cancelled':
        return 'Reactivate Plan';
      case 'service_not_included':
        return 'Upgrade Plan';
      default:
        return 'Choose Plan';
    }
  };

  return (
    <View style={styles.container}>
      <Card style={styles.accessDeniedCard}>
        <Card.Content style={styles.cardContent}>
          {/* Service Header */}
          <View style={styles.serviceHeader}>
            <View style={styles.serviceIconContainer}>
              <Icon name={service.icon} size={48} color={COLORS.primary} />
            </View>
            <View style={styles.serviceInfo}>
              <Text style={styles.serviceName}>{service.name}</Text>
              <Text style={styles.serviceDescription}>{service.description}</Text>
            </View>
          </View>

          {/* Premium Badge */}
          <View style={styles.premiumBadge}>
            <Chip style={styles.premiumChip} textStyle={styles.premiumChipText} icon="crown">
              Premium Feature
            </Chip>
          </View>

          {/* Reason Message */}
          <View style={styles.reasonContainer}>
            <Icon name="lock" size={20} color={COLORS.textSecondary} />
            <Text style={styles.reasonText}>{getReasonMessage()}</Text>
          </View>

          {/* Features List */}
          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>What you'll get:</Text>
            {service.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Icon name="check-circle" size={16} color={COLORS.success} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>

          {/* Security Notice */}
          <View style={styles.securityNotice}>
            <Icon name="shield-check" size={16} color={COLORS.success} />
            <Text style={styles.securityText}>
              Military-grade AES-256 encryption • Zero-knowledge architecture
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={onAccessDenied}
              style={styles.cancelButton}
            >
              Not Now
            </Button>
            <Button
              mode="contained"
              onPress={onUpgrade}
              style={styles.upgradeButton}
              contentStyle={styles.upgradeButtonContent}
            >
              {getActionText()}
            </Button>
          </View>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
  },
  accessDeniedCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardContent: {
    padding: SPACING.xl,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  serviceIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(74, 144, 226, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  serviceDescription: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  premiumBadge: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  premiumChip: {
    backgroundColor: COLORS.primary,
  },
  premiumChipText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  reasonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.lg,
  },
  reasonText: {
    flex: 1,
    fontSize: FONTS.sizes.medium,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    lineHeight: 20,
  },
  featuresContainer: {
    marginBottom: SPACING.lg,
  },
  featuresTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: SPACING.sm,
    borderRadius: 6,
    marginBottom: SPACING.lg,
  },
  securityText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.success,
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  upgradeButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
  },
  upgradeButtonContent: {
    paddingVertical: SPACING.xs,
  },
});

export default ServiceAccessGuard;
