import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';
import { serviceAccessService, UserSubscription, ServiceAccess } from '../services/ServiceAccessService';

interface SubscriptionStatusCardProps {
  onManageSubscription?: () => void;
  showManageButton?: boolean;
}

const SubscriptionStatusCard: React.FC<SubscriptionStatusCardProps> = ({
  onManageSubscription,
  showManageButton = true,
}) => {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [serviceAccess, setServiceAccess] = useState<ServiceAccess | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    setIsLoading(true);
    try {
      const [subscriptionData, accessData] = await Promise.all([
        serviceAccessService.getUserSubscription(),
        serviceAccessService.getAllServiceAccess(),
      ]);
      
      setSubscription(subscriptionData);
      setServiceAccess(accessData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleManageSubscription = () => {
    if (onManageSubscription) {
      onManageSubscription();
    } else {
      navigation.navigate('ModularPricing' as never);
    }
  };

  const handleUpgrade = () => {
    navigation.navigate('ModularPricing' as never);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return COLORS.success;
      case 'cancelled':
        return COLORS.warning;
      case 'past_due':
        return COLORS.error;
      default:
        return COLORS.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'cancelled':
        return 'Cancelled';
      case 'past_due':
        return 'Past Due';
      case 'incomplete':
        return 'Incomplete';
      default:
        return 'Unknown';
    }
  };

  const renderServiceChips = () => {
    if (!serviceAccess) return null;

    const services = [
      { id: 'contacts', name: 'Contacts', icon: 'account-multiple', hasAccess: serviceAccess.contacts },
      { id: 'messages', name: 'Messages', icon: 'message-text', hasAccess: serviceAccess.messages },
      { id: 'photos', name: 'Photos', icon: 'camera', hasAccess: serviceAccess.photos },
    ];

    return (
      <View style={styles.servicesContainer}>
        <Text style={styles.servicesTitle}>Your Services:</Text>
        <View style={styles.serviceChips}>
          {services.map(service => (
            <Chip
              key={service.id}
              style={[
                styles.serviceChip,
                service.hasAccess ? styles.activeServiceChip : styles.inactiveServiceChip
              ]}
              textStyle={[
                styles.serviceChipText,
                service.hasAccess ? styles.activeServiceChipText : styles.inactiveServiceChipText
              ]}
              icon={service.icon}
            >
              {service.name}
            </Chip>
          ))}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <Card style={styles.card}>
        <Card.Content style={styles.loadingContent}>
          <ActivityIndicator size="small" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading subscription...</Text>
        </Card.Content>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card style={styles.card}>
        <Card.Content style={styles.cardContent}>
          <View style={styles.noSubscriptionHeader}>
            <Icon name="crown-outline" size={32} color={COLORS.primary} />
            <Text style={styles.noSubscriptionTitle}>No Active Subscription</Text>
          </View>
          
          <Text style={styles.noSubscriptionDescription}>
            Subscribe to SafeKeep to securely backup your contacts, messages, and photos with military-grade encryption.
          </Text>

          <View style={styles.featuresPreview}>
            <View style={styles.featureItem}>
              <Icon name="shield-check" size={16} color={COLORS.success} />
              <Text style={styles.featureText}>AES-256 Encryption</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="cloud-sync" size={16} color={COLORS.success} />
              <Text style={styles.featureText}>Automatic Backup</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="devices" size={16} color={COLORS.success} />
              <Text style={styles.featureText}>Cross-Device Sync</Text>
            </View>
          </View>

          <Button
            mode="contained"
            onPress={handleUpgrade}
            style={styles.subscribeButton}
            contentStyle={styles.subscribeButtonContent}
          >
            Choose Your Plan
          </Button>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={styles.card}>
      <Card.Content style={styles.cardContent}>
        <View style={styles.subscriptionHeader}>
          <View style={styles.planInfo}>
            <Text style={styles.planName}>{subscription.planName}</Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(subscription.status) }]} />
              <Text style={[styles.statusText, { color: getStatusColor(subscription.status) }]}>
                {getStatusText(subscription.status)}
              </Text>
            </View>
          </View>
          <View style={styles.priceInfo}>
            <Text style={styles.price}>
              ${(subscription.priceCents / 100).toFixed(2)}
            </Text>
            <Text style={styles.pricePeriod}>/month</Text>
          </View>
        </View>

        {renderServiceChips()}

        {subscription.currentPeriodEnd && (
          <View style={styles.billingInfo}>
            <Icon name="calendar" size={16} color={COLORS.textSecondary} />
            <Text style={styles.billingText}>
              Next billing: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </Text>
          </View>
        )}

        {showManageButton && (
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={handleManageSubscription}
              style={styles.manageButton}
            >
              Manage Plan
            </Button>
            {subscription.status === 'active' && (
              <Button
                mode="contained"
                onPress={handleUpgrade}
                style={styles.upgradeButton}
              >
                Upgrade
              </Button>
            )}
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.lg,
  },
  loadingText: {
    marginLeft: SPACING.sm,
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
  },
  noSubscriptionHeader: {
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  noSubscriptionTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.sm,
  },
  noSubscriptionDescription: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  featuresPreview: {
    marginBottom: SPACING.lg,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
  },
  subscribeButton: {
    backgroundColor: COLORS.primary,
  },
  subscribeButtonContent: {
    paddingVertical: SPACING.xs,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SPACING.xs,
  },
  statusText: {
    fontSize: FONTS.sizes.small,
    fontWeight: '500',
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  pricePeriod: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
  },
  servicesContainer: {
    marginBottom: SPACING.md,
  },
  servicesTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  serviceChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  serviceChip: {
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  activeServiceChip: {
    backgroundColor: COLORS.primary,
  },
  inactiveServiceChip: {
    backgroundColor: COLORS.border,
  },
  serviceChipText: {
    fontSize: FONTS.sizes.small,
  },
  activeServiceChipText: {
    color: '#FFFFFF',
  },
  inactiveServiceChipText: {
    color: COLORS.textSecondary,
  },
  billingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  billingText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  manageButton: {
    flex: 1,
  },
  upgradeButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
});

export default SubscriptionStatusCard;
