-- SafeKeep Modular Pricing RLS Policies
-- Migration 008: Row Level Security policies for modular pricing tables
-- Run this script after 007_pricing_calculation_functions.sql

-- ============================================================================
-- STEP 1: Enable RLS on new tables
-- ============================================================================

-- Enable RLS on all modular pricing tables
ALTER TABLE public.service_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plan_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_service_selections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legacy_tier_mapping ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: Service Types Policies (Public read access)
-- ============================================================================

-- Anyone can view active service types
CREATE POLICY "Anyone can view active service types" ON public.service_types
    FOR SELECT USING (is_active = TRUE);

-- Only service role can modify service types
CREATE POLICY "Service role can manage service types" ON public.service_types
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 3: Subscription Plans Policies (Public read access)
-- ============================================================================

-- Anyone can view active subscription plans
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = TRUE);

-- Only service role can modify subscription plans
CREATE POLICY "Service role can manage subscription plans" ON public.subscription_plans
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 4: Plan Services Policies (Public read access)
-- ============================================================================

-- Anyone can view plan services for active plans
CREATE POLICY "Anyone can view plan services" ON public.plan_services
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.subscription_plans sp 
            WHERE sp.id = plan_id AND sp.is_active = TRUE
        )
    );

-- Only service role can modify plan services
CREATE POLICY "Service role can manage plan services" ON public.plan_services
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 5: User Service Selections Policies (User-specific access)
-- ============================================================================

-- Users can view their own service selections
CREATE POLICY "Users can view own service selections" ON public.user_service_selections
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own service selections
CREATE POLICY "Users can insert own service selections" ON public.user_service_selections
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own service selections
CREATE POLICY "Users can update own service selections" ON public.user_service_selections
    FOR UPDATE USING (auth.uid() = user_id);

-- Users cannot delete service selections (only deactivate)
CREATE POLICY "Users cannot delete service selections" ON public.user_service_selections
    FOR DELETE USING (FALSE);

-- Service role can manage all service selections
CREATE POLICY "Service role can manage all service selections" ON public.user_service_selections
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 6: User Subscriptions Policies (User-specific access)
-- ============================================================================

-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own subscriptions
CREATE POLICY "Users can insert own subscriptions" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own subscriptions
CREATE POLICY "Users can update own subscriptions" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Users cannot delete subscriptions (only cancel)
CREATE POLICY "Users cannot delete subscriptions" ON public.user_subscriptions
    FOR DELETE USING (FALSE);

-- Service role can manage all subscriptions
CREATE POLICY "Service role can manage all subscriptions" ON public.user_subscriptions
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 7: Legacy Tier Mapping Policies (Public read access)
-- ============================================================================

-- Anyone can view legacy tier mappings
CREATE POLICY "Anyone can view legacy tier mappings" ON public.legacy_tier_mapping
    FOR SELECT USING (TRUE);

-- Only service role can modify legacy tier mappings
CREATE POLICY "Service role can manage legacy tier mappings" ON public.legacy_tier_mapping
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- STEP 8: Grant table permissions to authenticated users
-- ============================================================================

-- Grant SELECT permissions to authenticated users for public tables
GRANT SELECT ON public.service_types TO authenticated;
GRANT SELECT ON public.subscription_plans TO authenticated;
GRANT SELECT ON public.plan_services TO authenticated;
GRANT SELECT ON public.legacy_tier_mapping TO authenticated;

-- Grant full permissions to authenticated users for their own data
GRANT SELECT, INSERT, UPDATE ON public.user_service_selections TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_subscriptions TO authenticated;

-- Grant permissions on views
GRANT SELECT ON public.service_combination_pricing TO authenticated;
GRANT SELECT ON public.user_subscription_summary TO authenticated;

-- ============================================================================
-- STEP 9: Create security definer functions for admin operations
-- ============================================================================

-- Function to safely update user storage quota based on services
CREATE OR REPLACE FUNCTION public.update_user_storage_quota(p_user_id UUID)
RETURNS BIGINT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_quota BIGINT;
BEGIN
    -- Only allow users to update their own quota or service role
    IF auth.uid() != p_user_id AND auth.role() != 'service_role' THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    
    -- Calculate new quota
    SELECT public.calculate_user_storage_quota(p_user_id) INTO new_quota;
    
    -- Update user's storage quota
    UPDATE public.users 
    SET storage_quota = new_quota
    WHERE id = p_user_id;
    
    RETURN new_quota;
END;
$$;

-- Function to safely check service access
CREATE OR REPLACE FUNCTION public.check_user_service_access(
    p_user_id UUID,
    p_service_type_id VARCHAR(50)
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Only allow users to check their own access or service role
    IF auth.uid() != p_user_id AND auth.role() != 'service_role' THEN
        RETURN FALSE;
    END IF;
    
    RETURN public.user_has_service_access(p_user_id, p_service_type_id);
END;
$$;

-- ============================================================================
-- STEP 10: Create triggers for automatic quota updates
-- ============================================================================

-- Function to automatically update storage quota when services change
CREATE OR REPLACE FUNCTION public.trigger_update_storage_quota()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update storage quota when service selections change
    PERFORM public.update_user_storage_quota(
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.user_id
            ELSE NEW.user_id
        END
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Create trigger on user_service_selections
DROP TRIGGER IF EXISTS trigger_service_selection_quota_update ON public.user_service_selections;
CREATE TRIGGER trigger_service_selection_quota_update
    AFTER INSERT OR UPDATE OR DELETE ON public.user_service_selections
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_update_storage_quota();

-- ============================================================================
-- STEP 11: Grant permissions on new functions
-- ============================================================================

GRANT EXECUTE ON FUNCTION public.update_user_storage_quota(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_user_service_access(UUID, VARCHAR(50)) TO authenticated;

-- ============================================================================
-- STEP 12: Add comments for documentation
-- ============================================================================

COMMENT ON POLICY "Anyone can view active service types" ON public.service_types IS 'Public read access to active service types for pricing display';
COMMENT ON POLICY "Anyone can view active subscription plans" ON public.subscription_plans IS 'Public read access to active plans for pricing display';
COMMENT ON POLICY "Users can view own service selections" ON public.user_service_selections IS 'Users can only see their own service selections';
COMMENT ON POLICY "Users can view own subscriptions" ON public.user_subscriptions IS 'Users can only see their own subscription details';

COMMENT ON FUNCTION public.update_user_storage_quota(UUID) IS 'Safely updates user storage quota based on selected services';
COMMENT ON FUNCTION public.check_user_service_access(UUID, VARCHAR(50)) IS 'Safely checks if user has access to specific service';
COMMENT ON FUNCTION public.trigger_update_storage_quota() IS 'Automatically updates storage quota when service selections change';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Modular pricing RLS policies created successfully!';
    RAISE NOTICE 'Security features enabled:';
    RAISE NOTICE '- Row Level Security on all tables';
    RAISE NOTICE '- User-specific access controls';
    RAISE NOTICE '- Public read access for pricing data';
    RAISE NOTICE '- Automatic storage quota updates';
    RAISE NOTICE '- Security definer functions for safe operations';
END $$;
