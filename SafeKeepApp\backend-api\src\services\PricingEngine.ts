import { PricingResult, ServiceCombination, PlanRecommendation, ServiceValidationResult } from '../types/modular-pricing';
import { supabase } from '../utils/database';

export class PricingEngine {
  /**
   * Validate service combination before pricing calculation
   */
  async validateServiceCombination(serviceIds: string[]): Promise<ServiceValidationResult> {
    try {
      // Check if all service IDs are valid and active
      const { data: validServices, error } = await supabase
        .from('service_types')
        .select('id, name, is_active')
        .in('id', serviceIds)
        .eq('is_active', true);

      if (error) {
        return {
          isValid: false,
          errors: [`Database error: ${error.message}`],
          validServices: []
        };
      }

      const foundServiceIds = validServices?.map(s => s.id) || [];
      const invalidServices = serviceIds.filter(id => !foundServiceIds.includes(id));

      if (invalidServices.length > 0) {
        return {
          isValid: false,
          errors: [`Invalid or inactive services: ${invalidServices.join(', ')}`],
          validServices: foundServiceIds
        };
      }

      // Check for duplicate services
      const uniqueServices = [...new Set(serviceIds)];
      if (uniqueServices.length !== serviceIds.length) {
        return {
          isValid: false,
          errors: ['Duplicate services are not allowed'],
          validServices: foundServiceIds
        };
      }

      return {
        isValid: true,
        errors: [],
        validServices: foundServiceIds
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
        validServices: []
      };
    }
  }

  /**
   * Calculate optimal pricing for given service combination
   * Uses database function to find the most cost-effective plan
   */
  async calculateOptimalPrice(serviceIds: string[]): Promise<PricingResult> {
    try {
      // Validate input
      if (!serviceIds || serviceIds.length === 0) {
        throw new Error('Service IDs are required');
      }

      // Validate service combination first
      const validation = await this.validateServiceCombination(serviceIds);
      if (!validation.isValid) {
        throw new Error(`Invalid service combination: ${validation.errors.join(', ')}`);
      }

      // Call database function to calculate optimal pricing
      const { data, error } = await supabase.rpc('calculate_optimal_pricing', {
        selected_services: serviceIds
      });

      if (error) {
        console.error('Error calculating optimal price:', error);
        throw new Error(`Failed to calculate optimal price: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('No pricing data found for the selected services');
      }

      const result = data[0];

      return {
        recommendedPlanId: result.optimal_plan_id || 'custom',
        recommendedPlanName: result.optimal_plan_name || 'Custom Selection',
        priceCents: result.optimal_price_cents || 0,
        savingsCents: result.savings_cents || 0,
        individualTotalCents: result.individual_total_cents || 0,
        totalStorageGb: result.total_storage_gb || 0,
        savingsPercentage: result.savings_percentage || 0
      };
    } catch (error) {
      console.error('PricingEngine.calculateOptimalPrice error:', error);
      throw error;
    }
  }

  /**
   * Calculate pricing for multiple service combinations at once
   */
  async calculateBulkPricing(serviceCombinations: string[][]): Promise<PricingResult[]> {
    try {
      const results: PricingResult[] = [];

      for (const serviceIds of serviceCombinations) {
        try {
          const pricing = await this.calculateOptimalPrice(serviceIds);
          results.push(pricing);
        } catch (error) {
          console.error(`Error calculating pricing for services ${serviceIds.join(', ')}:`, error);
          // Add a default result for failed calculations
          results.push({
            recommendedPlanId: 'error',
            recommendedPlanName: 'Error',
            priceCents: 0,
            savingsCents: 0,
            individualTotalCents: 0,
            totalStorageGb: 0,
            savingsPercentage: 0
          });
        }
      }

      return results;
    } catch (error) {
      console.error('PricingEngine.calculateBulkPricing error:', error);
      throw error;
    }
  }

  /**
   * Get individual service pricing
   */
  async getIndividualServicePricing(): Promise<{ [serviceId: string]: number }> {
    try {
      const { data, error } = await supabase
        .from('service_types')
        .select('id, base_price_cents')
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to get service pricing: ${error.message}`);
      }

      const pricing: { [serviceId: string]: number } = {};
      data?.forEach(service => {
        pricing[service.id] = service.base_price_cents;
      });

      return pricing;
    } catch (error) {
      console.error('PricingEngine.getIndividualServicePricing error:', error);
      throw error;
    }
  }

  /**
   * Get all available service combinations
   * Retrieves all active subscription plans with their service combinations
   */
  async getAvailableServiceCombinations(): Promise<ServiceCombination[]> {
    try {
      // Call database function to get available service combinations
      const { data, error } = await supabase.rpc('get_available_service_combinations');

      if (error) {
        console.error('Error getting service combinations:', error);
        throw new Error(`Failed to get service combinations: ${error.message}`);
      }

      if (!data) {
        return [];
      }

      // Transform database result to ServiceCombination interface
      return data.map((item: any) => ({
        planId: item.plan_id,
        planName: item.plan_name,
        priceCents: item.price_cents,
        services: item.services || [],
        storageGb: item.storage_gb || 0,
        isPopular: item.is_popular || false
      }));
    } catch (error) {
      console.error('PricingEngine.getAvailableServiceCombinations error:', error);
      throw error;
    }
  }

  /**
   * Get plan recommendations for a specific user
   * Suggests upgrades/downgrades based on usage patterns
   */
  async getPlanRecommendations(userId: string): Promise<PlanRecommendation[]> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Call database function to get plan recommendations
      const { data, error } = await supabase.rpc('get_plan_recommendations', {
        user_uuid: userId
      });

      if (error) {
        console.error('Error getting plan recommendations:', error);
        throw new Error(`Failed to get plan recommendations: ${error.message}`);
      }

      if (!data) {
        return [];
      }

      // Transform database result to PlanRecommendation interface
      return data.map((item: any) => ({
        planId: item.plan_id,
        planName: item.plan_name,
        priceCents: item.price_cents,
        reason: item.reason || '',
        savingsCents: Math.abs(item.price_difference_cents || 0)
      }));
    } catch (error) {
      console.error('PricingEngine.getPlanRecommendations error:', error);
      throw error;
    }
  }
}