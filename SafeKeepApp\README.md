# SafeKeep - Secure Personal Data Backup

SafeKeep is a comprehensive mobile application designed to securely backup and manage your personal data including contacts, messages, and photos. Built with React Native and powered by military-grade encryption, SafeKeep ensures your data remains private and secure.

## 🔐 Security Features

- **End-to-End Encryption**: AES-256 encryption with zero-knowledge architecture
- **Secure Key Management**: Hardware-backed key storage on supported devices
- **Privacy First**: Your data is encrypted before it leaves your device
- **GDPR & CCPA Compliant**: Full compliance with privacy regulations

## 📱 Core Features

### Data Backup
- **Contacts**: Secure backup and sync of your contact list with deduplication
- **Messages**: SMS and MMS backup with threading and search functionality
- **Photos**: Automatic photo backup with smart compression and organization
- **Cross-Platform**: Works on iOS and Android

### Advanced Features
- **Automatic Backup**: Set-and-forget scheduled backups
- **Incremental Sync**: Only backup changes to save bandwidth
- **Data Deduplication**: Intelligent duplicate detection
- **Restore Options**: Selective or full data restoration
- **Service Access Control**: Granular permissions for each backup service

## 💰 Modular Pricing

SafeKeep offers flexible modular pricing - pay only for the services you need:

### Individual Services
- **Contacts Backup** - $0.99/month
  - 1GB storage • Contact deduplication • Cross-device sync • Export options
- **Messages Backup** - $1.99/month
  - 2GB storage • SMS & MMS backup • Message threading • Search functionality
- **Photos Backup** - $4.99/month
  - 10GB storage • Automatic backup • Smart compression • Album organization

### Combination Plans (Save Money!)
- **Contacts + Messages** - $2.49/month *(Save $0.49)*
- **Contacts + Photos** - $5.49/month *(Save $0.49)*
- **Messages + Photos** - $6.49/month *(Save $0.49)* 🔥 **Most Popular**
- **Complete Backup** - $6.99/month *(Save $0.98)* 💡 **Best Value**

### Why Modular Pricing?
- **Pay for what you use** - No need to pay for services you don't need
- **Automatic savings** - Get discounts when you combine services
- **Flexible upgrades** - Add or remove services anytime
- **Transparent pricing** - No hidden fees or surprise charges

# Getting Started

> **Note**: Make sure you have completed the [Set Up Your Environment](https://reactnative.dev/docs/set-up-your-environment) guide before proceeding.

## Prerequisites

- Node.js
- npm or Yarn
- **Java Development Kit (JDK) 11 or newer** (required for Android development)
  - See our [Java Setup Guide](./docs/java-setup-guide.md) for detailed installation instructions
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## Step 1: Start Metro

First, you will need to run **Metro**, the JavaScript build tool for React Native.

To start the Metro dev server, run the following command from the root of your React Native project:

```sh
# Using npm
npm start

# OR using Yarn
yarn start
```

## Step 2: Build and run your app

With Metro running, open a new terminal window/pane from the root of your React Native project, and use one of the following commands to build and run your Android or iOS app:

### Android

Before building for Android, verify that you have the correct Java version installed:

```sh
# On Windows
.\android\verify-java-version.bat

# On macOS/Linux
./android/verify-java-version.sh
```

Then build and run the app:

```sh
# Using npm
npm run android

# OR using Yarn
yarn android
```

### iOS

For iOS, remember to install CocoaPods dependencies (this only needs to be run on first clone or after updating native deps).

The first time you create a new project, run the Ruby bundler to install CocoaPods itself:

```sh
bundle install
```

Then, and every time you update your native dependencies, run:

```sh
bundle exec pod install
```

For more information, please visit [CocoaPods Getting Started guide](https://guides.cocoapods.org/using/getting-started.html).

```sh
# Using npm
npm run ios

# OR using Yarn
yarn ios
```

If everything is set up correctly, you should see your new app running in the Android Emulator, iOS Simulator, or your connected device.

This is one way to run your app — you can also build it directly from Android Studio or Xcode.

## Step 3: Modify your app

Now that you have successfully run the app, let's make changes!

Open `App.tsx` in your text editor of choice and make some changes. When you save, your app will automatically update and reflect these changes — this is powered by [Fast Refresh](https://reactnative.dev/docs/fast-refresh).

When you want to forcefully reload, for example to reset the state of your app, you can perform a full reload:

- **Android**: Press the <kbd>R</kbd> key twice or select **"Reload"** from the **Dev Menu**, accessed via <kbd>Ctrl</kbd> + <kbd>M</kbd> (Windows/Linux) or <kbd>Cmd ⌘</kbd> + <kbd>M</kbd> (macOS).
- **iOS**: Press <kbd>R</kbd> in iOS Simulator.

## 🏗️ Architecture

SafeKeep is built with a modern, scalable architecture:

### Frontend
- **React Native**: Cross-platform mobile development
- **TypeScript**: Type-safe development
- **Redux Toolkit**: State management
- **React Navigation**: Navigation framework
- **React Native Paper**: Material Design components

### Backend
- **Node.js**: Server runtime
- **Express.js**: Web framework
- **Supabase**: Database and authentication
- **Stripe**: Payment processing
- **PostgreSQL**: Primary database

### Security
- **AES-256 Encryption**: Military-grade data encryption
- **Zero-Knowledge Architecture**: Data encrypted before leaving device
- **Hardware Security**: Device-backed key storage
- **GDPR/CCPA Compliance**: Privacy regulation compliance

## 📚 Documentation

### Core Documentation
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference for modular pricing
- **[User Flow Documentation](docs/USER_FLOW_DOCUMENTATION.md)** - User journey through service selection
- **[Service Permission System](docs/SERVICE_PERMISSION_SYSTEM.md)** - Access control and permissions
- **[Testing Checklist](docs/TESTING_CHECKLIST.md)** - Comprehensive testing guide

### Implementation Guides
- **[Stripe Setup Guide](STRIPE_MODULAR_SETUP.md)** - Payment integration setup
- **[Service Access System](SERVICE_ACCESS_SYSTEM.md)** - Permission system implementation
- **[Modular Pricing UI](src/screens/Subscription/MODULAR_PRICING_UI.md)** - UI component guide

### Project Management
- **[Tasks](TASKS.md)** - Implementation status and roadmap

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage
```

### Testing Checklist
See [Testing Checklist](docs/TESTING_CHECKLIST.md) for comprehensive testing scenarios including all 7 pricing combinations.

## 🔧 Configuration

### Environment Variables
```bash
# API Configuration
API_BASE_URL=http://localhost:3000/api
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key

# Feature Flags
ENABLE_MODULAR_PRICING=true
ENABLE_SERVICE_ACCESS_GUARDS=true
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](docs/) folder
- **Issues**: Create an issue on GitHub
- **Email**: <EMAIL>

# Troubleshooting

If you're having issues getting the above steps to work, see the [Troubleshooting](https://reactnative.dev/docs/troubleshooting) page.

For SafeKeep-specific issues, check our documentation in the [docs](docs/) folder.
