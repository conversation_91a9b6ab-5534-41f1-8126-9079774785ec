<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Modular Pricing Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            align-items: start;
        }

        .phone-container {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            width: 375px;
            height: 812px;
            margin: 0 auto;
            position: relative;
        }

        .phone-header {
            background: #4A90E2;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .phone-header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .screen {
            height: calc(100% - 60px);
            overflow-y: auto;
            padding: 20px;
        }

        .header-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .header-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .header-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .header-subtitle {
            font-size: 16px;
            color: #666;
            line-height: 1.4;
        }

        .services-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .service-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .service-card.selected {
            border-color: #4A90E2;
            background: rgba(74, 144, 226, 0.05);
        }

        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .service-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
        }

        .service-card.selected .service-icon {
            background: rgba(74, 144, 226, 0.2);
        }

        .service-info {
            flex: 1;
        }

        .service-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .service-card.selected .service-name {
            color: #4A90E2;
        }

        .service-description {
            font-size: 14px;
            color: #666;
        }

        .service-price {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .service-card.selected .service-price {
            color: #4A90E2;
        }

        .service-features {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e9ecef;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 13px;
            color: #666;
        }

        .feature-icon {
            color: #28a745;
            margin-right: 8px;
            font-size: 12px;
        }

        .checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .service-card.selected .checkbox {
            background: #4A90E2;
            border-color: #4A90E2;
            color: white;
        }

        .pricing-summary {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .pricing-summary.active {
            border-color: #4A90E2;
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
        }

        .plan-badge {
            background: #4A90E2;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 12px;
        }

        .popular-badge {
            background: #ff6b6b;
        }

        .plan-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .plan-price {
            font-size: 28px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 12px;
        }

        .savings-highlight {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .selected-services {
            margin-bottom: 16px;
        }

        .selected-services-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .service-chip {
            display: inline-block;
            background: #4A90E2;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 6px;
            margin-bottom: 4px;
        }

        .continue-btn {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .continue-btn:hover {
            background: #357abd;
        }

        .continue-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .security-notice {
            background: rgba(40, 167, 69, 0.05);
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 20px;
            text-align: center;
        }

        .security-title {
            font-size: 14px;
            font-weight: 600;
            color: #28a745;
            margin-bottom: 4px;
        }

        .security-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .info-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-comparison {
            margin-bottom: 25px;
        }

        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .comparison-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .comparison-item:last-child {
            border-bottom: none;
        }

        .old-pricing {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        .new-pricing {
            color: #28a745;
            font-weight: 600;
        }

        .demo-controls {
            margin-bottom: 20px;
        }

        .demo-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }

        .demo-btn:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .demo-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .phone-container {
                width: 100%;
                max-width: 375px;
                height: 600px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Phone Demo -->
        <div class="phone-container">
            <div class="phone-header">
                <button class="back-btn">←</button>
                <h1>Choose Your Services</h1>
                <div></div>
            </div>
            
            <div class="screen">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-icon">🛡️</div>
                    <div class="header-title">Mix & Match Your Perfect Plan</div>
                    <div class="header-subtitle">Select only the services you need. Get automatic savings on combinations with military-grade encryption for all your data.</div>
                </div>

                <!-- Services Section -->
                <div class="services-section">
                    <div class="section-title">Available Services</div>
                    
                    <!-- Contacts Service -->
                    <div class="service-card" data-service="contacts" data-price="99">
                        <div class="checkbox"></div>
                        <div class="service-header">
                            <div class="service-icon">👥</div>
                            <div class="service-info">
                                <div class="service-name">Contacts Backup</div>
                                <div class="service-description">Secure backup and sync of your contacts</div>
                            </div>
                            <div class="service-price">$0.99/mo</div>
                        </div>
                        <div class="service-features">
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                Contact deduplication
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                Cross-device sync
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                1GB storage
                            </div>
                        </div>
                    </div>

                    <!-- Messages Service -->
                    <div class="service-card" data-service="messages" data-price="199">
                        <div class="checkbox"></div>
                        <div class="service-header">
                            <div class="service-icon">💬</div>
                            <div class="service-info">
                                <div class="service-name">Messages Backup</div>
                                <div class="service-description">Secure backup and sync of your messages</div>
                            </div>
                            <div class="service-price">$1.99/mo</div>
                        </div>
                        <div class="service-features">
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                SMS & MMS backup
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                Message threading
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                2GB storage
                            </div>
                        </div>
                    </div>

                    <!-- Photos Service -->
                    <div class="service-card" data-service="photos" data-price="499">
                        <div class="checkbox"></div>
                        <div class="service-header">
                            <div class="service-icon">📸</div>
                            <div class="service-info">
                                <div class="service-name">Photos Backup</div>
                                <div class="service-description">Secure backup and sync of your photos</div>
                            </div>
                            <div class="service-price">$4.99/mo</div>
                        </div>
                        <div class="service-features">
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                Automatic photo backup
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                Smart compression
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                10GB storage
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Summary -->
                <div class="pricing-summary" id="pricing-summary" style="display: none;">
                    <div class="plan-badge" id="plan-badge">Recommended</div>
                    <div class="plan-name" id="plan-name">Select services to see pricing</div>
                    <div class="plan-price" id="plan-price">$0.00/month</div>
                    <div class="savings-highlight" id="savings-highlight" style="display: none;">
                        💰 Save $0.00 compared to individual services
                    </div>
                    <div class="selected-services">
                        <div class="selected-services-title">Included services:</div>
                        <div id="selected-services-list"></div>
                    </div>
                </div>

                <!-- Continue Button -->
                <button class="continue-btn" id="continue-btn" disabled>
                    Select services to continue
                </button>

                <!-- Security Notice -->
                <div class="security-notice">
                    <div class="security-title">🔒 Your Data is Secure</div>
                    <div class="security-text">
                        All services include end-to-end AES-256 encryption, zero-knowledge architecture, and GDPR compliance.
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="info-panel">
            <div class="info-title">🎯 Modular Pricing Demo</div>
            
            <div class="demo-controls">
                <div class="comparison-title">Try These Combinations:</div>
                <button class="demo-btn" onclick="selectServices(['contacts'])">Contacts Only</button>
                <button class="demo-btn" onclick="selectServices(['messages'])">Messages Only</button>
                <button class="demo-btn" onclick="selectServices(['photos'])">Photos Only</button>
                <button class="demo-btn" onclick="selectServices(['contacts', 'messages'])">Contacts + Messages</button>
                <button class="demo-btn" onclick="selectServices(['messages', 'photos'])">Messages + Photos 🔥</button>
                <button class="demo-btn" onclick="selectServices(['contacts', 'messages', 'photos'])">Complete Backup 💡</button>
                <button class="demo-btn" onclick="selectServices([])">Clear All</button>
            </div>

            <div class="feature-comparison">
                <div class="comparison-title">💰 Pricing Benefits</div>
                <div class="comparison-item">
                    <span>Individual Services Total</span>
                    <span class="old-pricing">$7.97/month</span>
                </div>
                <div class="comparison-item">
                    <span>Complete Backup Bundle</span>
                    <span class="new-pricing">$6.99/month</span>
                </div>
                <div class="comparison-item">
                    <span><strong>Your Savings</strong></span>
                    <span class="new-pricing"><strong>$0.98/month</strong></span>
                </div>
            </div>

            <div class="feature-comparison">
                <div class="comparison-title">✨ Key Features</div>
                <div class="comparison-item">
                    <span>Pay for what you use</span>
                    <span>✅</span>
                </div>
                <div class="comparison-item">
                    <span>Automatic savings detection</span>
                    <span>✅</span>
                </div>
                <div class="comparison-item">
                    <span>Flexible upgrades/downgrades</span>
                    <span>✅</span>
                </div>
                <div class="comparison-item">
                    <span>No hidden fees</span>
                    <span>✅</span>
                </div>
                <div class="comparison-item">
                    <span>Military-grade encryption</span>
                    <span>✅</span>
                </div>
            </div>

            <div class="feature-comparison">
                <div class="comparison-title">🏆 Popular Combinations</div>
                <div class="comparison-item">
                    <span>Messages + Photos</span>
                    <span class="new-pricing">Most Popular 🔥</span>
                </div>
                <div class="comparison-item">
                    <span>Complete Backup</span>
                    <span class="new-pricing">Best Value 💡</span>
                </div>
                <div class="comparison-item">
                    <span>Contacts + Messages</span>
                    <span class="new-pricing">Great Starter</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Service pricing data
        const SERVICES = {
            contacts: { name: 'Contacts', price: 99, storage: 1 },
            messages: { name: 'Messages', price: 199, storage: 2 },
            photos: { name: 'Photos', price: 499, storage: 10 }
        };

        // Combination plans with savings
        const COMBINATION_PLANS = {
            'contacts,messages': { name: 'Contacts + Messages', price: 249, savings: 49 },
            'contacts,photos': { name: 'Contacts + Photos', price: 549, savings: 49 },
            'messages,photos': { name: 'Messages + Photos', price: 649, savings: 49, isPopular: true },
            'contacts,messages,photos': { name: 'Complete Backup', price: 699, savings: 98, isBestValue: true }
        };

        let selectedServices = [];

        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const serviceCards = document.querySelectorAll('.service-card');
            serviceCards.forEach(card => {
                card.addEventListener('click', function() {
                    const serviceId = this.dataset.service;
                    toggleService(serviceId);
                });
            });
        });

        function toggleService(serviceId) {
            const index = selectedServices.indexOf(serviceId);
            if (index > -1) {
                selectedServices.splice(index, 1);
            } else {
                selectedServices.push(serviceId);
            }
            updateUI();
        }

        function selectServices(services) {
            selectedServices = [...services];
            updateUI();
        }

        function updateUI() {
            // Update service cards
            const serviceCards = document.querySelectorAll('.service-card');
            serviceCards.forEach(card => {
                const serviceId = card.dataset.service;
                const checkbox = card.querySelector('.checkbox');
                
                if (selectedServices.includes(serviceId)) {
                    card.classList.add('selected');
                    checkbox.innerHTML = '✓';
                } else {
                    card.classList.remove('selected');
                    checkbox.innerHTML = '';
                }
            });

            // Update pricing summary
            updatePricingSummary();
        }

        function updatePricingSummary() {
            const pricingSummary = document.getElementById('pricing-summary');
            const continueBtn = document.getElementById('continue-btn');

            if (selectedServices.length === 0) {
                pricingSummary.style.display = 'none';
                continueBtn.disabled = true;
                continueBtn.textContent = 'Select services to continue';
                return;
            }

            // Calculate pricing
            const individualTotal = selectedServices.reduce((total, serviceId) => {
                return total + SERVICES[serviceId].price;
            }, 0);

            // Check for combination plan
            const sortedServices = [...selectedServices].sort().join(',');
            const combinationPlan = COMBINATION_PLANS[sortedServices];

            let finalPrice = individualTotal;
            let savings = 0;
            let planName = 'Custom Plan';
            let badgeText = 'Custom';
            let badgeClass = '';

            if (combinationPlan) {
                finalPrice = combinationPlan.price;
                savings = individualTotal - finalPrice;
                planName = combinationPlan.name;
                
                if (combinationPlan.isPopular) {
                    badgeText = 'Most Popular 🔥';
                    badgeClass = 'popular-badge';
                } else if (combinationPlan.isBestValue) {
                    badgeText = 'Best Value 💡';
                } else {
                    badgeText = 'Recommended';
                }
            }

            // Calculate total storage
            const totalStorage = selectedServices.reduce((total, serviceId) => {
                return total + SERVICES[serviceId].storage;
            }, 0);

            // Update UI elements
            document.getElementById('plan-badge').textContent = badgeText;
            document.getElementById('plan-badge').className = `plan-badge ${badgeClass}`;
            document.getElementById('plan-name').textContent = planName;
            document.getElementById('plan-price').textContent = `$${(finalPrice / 100).toFixed(2)}/month`;

            // Update savings
            const savingsElement = document.getElementById('savings-highlight');
            if (savings > 0) {
                savingsElement.style.display = 'block';
                savingsElement.textContent = `💰 Save $${(savings / 100).toFixed(2)} compared to individual services`;
            } else {
                savingsElement.style.display = 'none';
            }

            // Update selected services list
            const servicesList = document.getElementById('selected-services-list');
            servicesList.innerHTML = selectedServices.map(serviceId => 
                `<span class="service-chip">${SERVICES[serviceId].name}</span>`
            ).join('');

            // Update continue button
            continueBtn.disabled = false;
            continueBtn.textContent = `Continue with ${selectedServices.length} service${selectedServices.length > 1 ? 's' : ''} - $${(finalPrice / 100).toFixed(2)}/mo`;

            // Show pricing summary
            pricingSummary.style.display = 'block';
            pricingSummary.classList.add('active');
        }

        // Demo button click handler
        document.getElementById('continue-btn').addEventListener('click', function() {
            if (selectedServices.length > 0) {
                const finalPrice = calculateFinalPrice();
                const planName = getPlanName();
                alert(`🎉 Demo: Proceeding to payment for ${planName} at $${(finalPrice / 100).toFixed(2)}/month\n\nSelected services: ${selectedServices.map(id => SERVICES[id].name).join(', ')}`);
            }
        });

        function calculateFinalPrice() {
            const individualTotal = selectedServices.reduce((total, serviceId) => {
                return total + SERVICES[serviceId].price;
            }, 0);

            const sortedServices = [...selectedServices].sort().join(',');
            const combinationPlan = COMBINATION_PLANS[sortedServices];

            return combinationPlan ? combinationPlan.price : individualTotal;
        }

        function getPlanName() {
            const sortedServices = [...selectedServices].sort().join(',');
            const combinationPlan = COMBINATION_PLANS[sortedServices];
            return combinationPlan ? combinationPlan.name : 'Custom Plan';
        }

        // Auto-demo: Show popular combination after 3 seconds
        setTimeout(() => {
            selectServices(['messages', 'photos']);
        }, 3000);
    </script>
</body>
</html>
