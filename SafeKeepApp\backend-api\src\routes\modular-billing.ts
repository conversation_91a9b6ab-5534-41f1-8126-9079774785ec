import { Router } from 'express';
import ModularBillingController from '../controllers/ModularBillingController';
import { authenticateToken } from '../middleware/auth';
import { validateServiceIds } from '../middleware/serviceValidation';

const router = Router();
const billingController = new ModularBillingController();

/**
 * Modular billing routes for SafeKeep
 * Handles payment intents, subscriptions, and webhooks for the new modular pricing system
 */

// Payment Intent Routes
/**
 * POST /api/billing/payment-intent
 * Create payment intent for modular subscription
 * 
 * Body:
 * {
 *   "amount": 699,
 *   "currency": "usd",
 *   "serviceIds": ["contacts", "messages", "photos"],
 *   "userId": "user-uuid",
 *   "planId": "complete-backup",
 *   "planName": "Complete Backup",
 *   "customerId": "cus_stripe_id",
 *   "description": "SafeKeep Complete Backup Subscription"
 * }
 */
router.post('/payment-intent', authenticateToken, validateServiceIds, (req, res) => {
  billingController.createPaymentIntent(req, res);
});

// Subscription Management Routes
/**
 * POST /api/billing/subscriptions
 * Create new modular subscription
 * 
 * Body:
 * {
 *   "userId": "user-uuid",
 *   "serviceIds": ["contacts", "messages"],
 *   "planId": "contacts-messages",
 *   "priceCents": 249,
 *   "customerId": "cus_stripe_id",
 *   "paymentMethodId": "pm_stripe_id",
 *   "billingEmail": "<EMAIL>"
 * }
 */
router.post('/subscriptions', authenticateToken, validateServiceIds, (req, res) => {
  billingController.createSubscription(req, res);
});

/**
 * PUT /api/billing/subscriptions/:subscriptionId
 * Update existing subscription with new services
 * 
 * Body:
 * {
 *   "serviceIds": ["contacts", "messages", "photos"],
 *   "planId": "complete-backup",
 *   "priceCents": 699,
 *   "prorationBehavior": "create_prorations"
 * }
 */
router.put('/subscriptions/:subscriptionId', authenticateToken, validateServiceIds, (req, res) => {
  billingController.updateSubscription(req, res);
});

/**
 * GET /api/billing/subscriptions/:subscriptionId
 * Get subscription details from Stripe
 */
router.get('/subscriptions/:subscriptionId', authenticateToken, (req, res) => {
  billingController.getSubscriptionDetails(req, res);
});

/**
 * DELETE /api/billing/subscriptions/:subscriptionId
 * Cancel subscription (cancel at period end)
 */
router.delete('/subscriptions/:subscriptionId', authenticateToken, (req, res) => {
  billingController.cancelSubscription(req, res);
});

// Webhook Routes
/**
 * POST /api/billing/webhook
 * Handle Stripe webhooks for modular subscriptions
 * 
 * This endpoint should be configured in your Stripe dashboard to receive:
 * - customer.subscription.created
 * - customer.subscription.updated
 * - customer.subscription.deleted
 * - invoice.payment_succeeded
 * - invoice.payment_failed
 * - customer.created
 * - customer.updated
 */
router.post('/webhook', (req, res) => {
  // Note: No authentication middleware for webhooks - Stripe signature verification is used instead
  billingController.handleWebhook(req, res);
});

export default router;
