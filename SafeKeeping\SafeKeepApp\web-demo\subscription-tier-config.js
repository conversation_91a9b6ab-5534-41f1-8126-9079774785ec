/**
 * Subscription Tier Configuration for SafeKeep Web Demo
 * Defines subscription tiers, features, and pricing
 */

class SubscriptionTierConfig {
    constructor() {
        // Initialize subscription tiers map
        this.tiers = new Map();
        
        // Define subscription tiers - Individual Services
        this.tiers.set('contacts_only', {
            id: 'contacts_only',
            name: 'Contacts Only',
            description: 'Secure backup for your contacts',
            price: 99, // $0.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 1,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: false,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#28a745',
            popular: false
        });

        this.tiers.set('messages_only', {
            id: 'messages_only',
            name: 'Messages Only',
            description: 'Secure backup for your messages',
            price: 199, // $1.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 1,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: true,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#17a2b8',
            popular: false
        });

        this.tiers.set('photos_only', {
            id: 'photos_only',
            name: 'Photos Only',
            description: 'Secure backup for your photos',
            price: 499, // $4.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 10,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: false,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#fd7e14',
            popular: false
        });

        // Combination Services
        this.tiers.set('contacts_messages', {
            id: 'contacts_messages',
            name: 'Contacts + Messages',
            description: 'Backup contacts and messages together',
            price: 249, // $2.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 2,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: true,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#6f42c1',
            popular: false
        });

        this.tiers.set('contacts_photos', {
            id: 'contacts_photos',
            name: 'Contacts + Photos',
            description: 'Backup contacts and photos together',
            price: 549, // $5.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 11,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: false,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#e83e8c',
            popular: false
        });

        this.tiers.set('messages_photos', {
            id: 'messages_photos',
            name: 'Messages + Photos',
            description: 'Backup messages and photos together',
            price: 649, // $6.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 11,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: true,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#20c997',
            popular: true
        });

        this.tiers.set('complete_backup', {
            id: 'complete_backup',
            name: 'Complete Backup',
            description: 'Full backup solution for all your data',
            price: 699, // $6.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 12,
                maxBackupsPerMonth: -1, // unlimited
                maxRestoresPerMonth: -1, // unlimited
                backupFrequencies: ['manual', 'daily', 'weekly'],
                backupHistoryDays: 90
            },
            features: {
                contacts_backup: true,
                messages_backup: true,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: true,
                advanced_encryption: true,
                multi_device_sync: true,
                backup_history_extended: true
            },
            color: '#007bff',
            popular: false
        });
    }

    /**
     * Get all subscription tiers
     */
    getAllTiers() {
        return Array.from(this.tiers.values());
    }

    /**
     * Get tier by ID
     */
    getTier(tierId) {
        return this.tiers.get(tierId) || null;
    }

    /**
     * Get tiers by category
     */
    getTiersByCategory() {
        const individual = [];
        const combinations = [];
        
        this.tiers.forEach(tier => {
            const serviceCount = Object.values(tier.features)
                .filter(feature => feature === true && 
                    ['contacts_backup', 'messages_backup', 'photos_backup'].includes(feature))
                .length;
            
            if (serviceCount === 1) {
                individual.push(tier);
            } else {
                combinations.push(tier);
            }
        });
        
        return { individual, combinations };
    }

    /**
     * Calculate savings for combination tiers
     */
    calculateSavings(tierId) {
        const tier = this.getTier(tierId);
        if (!tier) return 0;
        
        // Calculate individual service costs
        let individualTotal = 0;
        if (tier.features.contacts_backup) individualTotal += 99;
        if (tier.features.messages_backup) individualTotal += 199;
        if (tier.features.photos_backup) individualTotal += 499;
        
        return Math.max(0, individualTotal - tier.price);
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionTierConfig = SubscriptionTierConfig;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionTierConfig;
}
