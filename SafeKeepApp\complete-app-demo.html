<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Complete App Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 30px;
            align-items: start;
        }

        .phone-container {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            width: 375px;
            height: 812px;
            position: relative;
        }

        .phone-header {
            background: #4A90E2;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .phone-header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .screen {
            height: calc(100% - 120px);
            overflow-y: auto;
            padding: 20px;
            display: none;
        }

        .screen.active {
            display: block;
        }

        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            height: 60px;
        }

        .tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #666;
            font-size: 12px;
            border: none;
            background: none;
            transition: color 0.3s;
        }

        .tab.active {
            color: #4A90E2;
        }

        .tab-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4A90E2;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 8px;
            transition: background 0.3s;
        }

        .button:hover {
            background: #357abd;
        }

        .button.secondary {
            background: none;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #50e3c2;
        }

        .status-warning {
            background: #f5a623;
        }

        .status-error {
            background: #d0021b;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .demo-controls {
            margin-bottom: 20px;
        }

        .demo-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }

        .demo-btn:hover {
            background: #5a6268;
        }

        .demo-btn.active {
            background: #4A90E2;
        }

        .backup-progress {
            margin: 16px 0;
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .backup-item:last-child {
            border-bottom: none;
        }

        .switch {
            width: 50px;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.on {
            background: #4A90E2;
        }

        .switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }

        .switch.on::after {
            transform: translateX(20px);
        }

        @media (max-width: 768px) {
            .demo-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .phone-container {
                width: 100%;
                max-width: 375px;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Phone Demo -->
        <div class="phone-container">
            <div class="phone-header">
                <button class="back-btn" onclick="goBack()">←</button>
                <h1 id="screen-title">SafeKeep</h1>
                <div></div>
            </div>

            <!-- Welcome Screen -->
            <div id="welcome-screen" class="screen active">
                <div style="text-align: center; padding: 40px 0;">
                    <div style="font-size: 80px; margin-bottom: 20px;">🛡️</div>
                    <h1 style="font-size: 32px; font-weight: bold; color: #333; margin-bottom: 8px;">SafeKeep</h1>
                    <p style="font-size: 18px; color: #666; margin-bottom: 32px;">Your Precious Data, Perfectly Protected</p>
                </div>

                <div class="card">
                    <p style="text-align: center; margin-bottom: 24px; color: #666; line-height: 1.5;">
                        Securely backup your photos, contacts, and messages with military-grade encryption.
                        Designed for everyone, especially families.
                    </p>

                    <ul class="feature-list">
                        <li class="feature-item">
                            <span class="feature-icon">📷</span>
                            <span>Automatic photo backup with deduplication</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">👥</span>
                            <span>Complete contact synchronization</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">💬</span>
                            <span>Secure message backup and threading</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">🔐</span>
                            <span>End-to-end AES-256 encryption</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">☁️</span>
                            <span>Supabase cloud storage integration</span>
                        </li>
                    </ul>

                    <button class="button" onclick="showScreen('auth-screen')">Get Started</button>
                </div>
            </div>

            <!-- Authentication Screen -->
            <div id="auth-screen" class="screen">
                <div style="text-align: center; padding: 40px 0 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Welcome Back</h1>
                    <p style="color: #666;">Sign in to access your secure backups</p>
                </div>

                <div class="card">
                    <div style="margin-bottom: 16px;">
                        <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="<EMAIL>">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="••••••••">
                    </div>
                    <button class="button" onclick="showScreen('onboarding-screen')">🔐 Sign In</button>
                    <button class="button secondary">Create Account</button>
                </div>

                <div class="card" style="background: rgba(80, 227, 194, 0.1);">
                    <div class="card-title">
                        <span>🔐</span>
                        <span>Your Data is Secure</span>
                    </div>
                    <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                        <li>• All data encrypted with AES-256 before upload</li>
                        <li>• Only you have the decryption keys</li>
                        <li>• We cannot access your personal information</li>
                        <li>• Your privacy is our top priority</li>
                    </ul>
                </div>
            </div>

            <!-- Onboarding Screen -->
            <div id="onboarding-screen" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Let's Set You Up</h1>
                    <p style="color: #666;">We need a few permissions to keep your data safe</p>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>📊</span>
                        <span>Setup Progress</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Progress</span>
                        <span style="color: #4A90E2; font-weight: bold;">67%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 67%;"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>🔐</span>
                        <span>Permissions Needed</span>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">📷</span>
                            <div>
                                <div style="font-weight: 600;">Photo Access</div>
                                <div style="font-size: 14px; color: #666;">Backup your precious family photos</div>
                            </div>
                        </div>
                        <span style="background: #50e3c2; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✓ Allowed</span>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">👥</span>
                            <div>
                                <div style="font-weight: 600;">Contact Access</div>
                                <div style="font-size: 14px; color: #666;">Save important phone numbers</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission('contacts')">Allow</button>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">💬</span>
                            <div>
                                <div style="font-weight: 600;">Message Access</div>
                                <div style="font-size: 14px; color: #666;">Backup important conversations</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission('messages')">Allow</button>
                    </div>
                </div>

                <button class="button" onclick="showScreen('subscription-screen')">Continue to Subscription</button>
            </div>

            <!-- Subscription Screen -->
            <div id="subscription-screen" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">💎</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Choose Your Plan</h1>
                    <p style="color: #666;">Mix & match services or get everything</p>
                </div>

                <div class="card" style="border: 2px solid #4A90E2;">
                    <div class="card-title">
                        <span>🏆</span>
                        <span>Complete Backup</span>
                        <span style="background: #4A90E2; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: auto;">RECOMMENDED</span>
                    </div>
                    <div style="font-size: 32px; font-weight: bold; color: #4A90E2; margin-bottom: 8px;">$6.99<span style="font-size: 16px;">/month</span></div>
                    <ul style="margin-bottom: 16px;">
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Photos + Contacts + Messages</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>100GB Storage</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Priority Support</span>
                        </li>
                    </ul>
                    <button class="button" onclick="selectPlan('complete')">Select Complete Plan</button>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>📱</span>
                        <span>Individual Services</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">📷</div>
                            <div style="font-size: 12px; color: #666;">Photos</div>
                            <div style="font-weight: bold; color: #4A90E2;">$4.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">👥</div>
                            <div style="font-size: 12px; color: #666;">Contacts</div>
                            <div style="font-weight: bold; color: #4A90E2;">$0.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">💬</div>
                            <div style="font-size: 12px; color: #666;">Messages</div>
                            <div style="font-weight: bold; color: #4A90E2;">$1.99</div>
                        </div>
                    </div>
                </div>

                <button class="button" onclick="showMainApp()">Start Free Trial</button>
            </div>

            <!-- Dashboard Screen -->
            <div id="dashboard-screen" class="screen">
                <div class="card">
                    <div class="card-title">
                        <span>📊</span>
                        <span>Backup Status</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div class="status-indicator status-success"></div>
                        <span>All data backed up</span>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        Last backup: Today at 2:30 PM
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">📷</div>
                        <div class="stat-number" id="photos-count">1,247</div>
                        <div class="stat-label">Photos</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">👥</div>
                        <div class="stat-number" id="contacts-count">156</div>
                        <div class="stat-label">Contacts</div>
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💬</div>
                        <div class="stat-number" id="messages-count">2,341</div>
                        <div class="stat-label">Messages</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💾</div>
                        <div class="stat-number">2.8 GB</div>
                        <div class="stat-label">Total Size</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>☁️</span>
                        <span>Cloud Storage</span>
                    </div>
                    <div style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 18px; color: #333; margin-bottom: 8px;">
                            2.8 GB of 100 GB used
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 2.8%;"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #666;">
                            <span>2.8% used</span>
                            <span>97.2 GB remaining</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>📈</span>
                        <span>Recent Activity</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">📷 Photo Backup</div>
                            <div style="font-size: 14px; color: #666;">23 new photos backed up</div>
                        </div>
                        <span style="color: #50e3c2; font-size: 12px;">2 min ago</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">👥 Contact Sync</div>
                            <div style="font-size: 14px; color: #666;">3 contacts updated</div>
                        </div>
                        <span style="color: #50e3c2; font-size: 12px;">1 hour ago</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">💬 Message Backup</div>
                            <div style="font-size: 14px; color: #666;">15 new messages backed up</div>
                        </div>
                        <span style="color: #50e3c2; font-size: 12px;">3 hours ago</span>
                    </div>
                </div>
            </div>

            <!-- Backup Screen -->
            <div id="backup-screen" class="screen">
                <div class="card">
                    <div class="card-title">
                        <span>🚀</span>
                        <span>Backup Center</span>
                    </div>
                    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                        <button class="button" onclick="startFullBackup()">Start Full Backup</button>
                        <button class="button secondary" onclick="showBackupHistory()">View History</button>
                    </div>
                    <div style="font-size: 14px; color: #666; text-align: center;">
                        Next automatic backup: Tomorrow at 2:30 PM
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>📷</span>
                        <span>Photos</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">1,247 photos backed up</div>
                        </div>
                        <div class="status-indicator status-success"></div>
                    </div>
                    <div class="backup-progress">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="startPhotoBackup()">Backup Photos Now</button>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>👥</span>
                        <span>Contacts</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">156 contacts synced</div>
                        </div>
                        <div class="status-indicator status-success"></div>
                    </div>
                    <div class="backup-progress">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="startContactBackup()">Sync Contacts Now</button>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>💬</span>
                        <span>Messages</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">2,341 messages backed up</div>
                        </div>
                        <div class="status-indicator status-success"></div>
                    </div>
                    <div class="backup-progress">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="startMessageBackup()">Backup Messages Now</button>
                </div>
            </div>

            <!-- Settings Screen -->
            <div id="settings-screen" class="screen">
                <div class="card">
                    <div class="card-title">
                        <span>👤</span>
                        <span>Account</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Email</div>
                            <div style="font-size: 14px; color: #666;"><EMAIL></div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Edit</button>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Subscription</div>
                            <div style="font-size: 14px; color: #666;">Complete Backup Plan</div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Manage</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>🔄</span>
                        <span>Backup Settings</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Auto Backup</div>
                            <div style="font-size: 14px; color: #666;">Automatically backup new data</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">WiFi Only</div>
                            <div style="font-size: 14px; color: #666;">Only backup on WiFi connection</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Backup Frequency</div>
                            <div style="font-size: 14px; color: #666;">Daily at 2:30 PM</div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Change</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>🔐</span>
                        <span>Security</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Encryption</div>
                            <div style="font-size: 14px; color: #666;">AES-256 encryption enabled</div>
                        </div>
                        <div class="status-indicator status-success"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Two-Factor Auth</div>
                            <div style="font-size: 14px; color: #666;">Extra security for your account</div>
                        </div>
                        <div class="switch" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Biometric Lock</div>
                            <div style="font-size: 14px; color: #666;">Use fingerprint to unlock app</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>📱</span>
                        <span>App Settings</span>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Notifications</div>
                            <div style="font-size: 14px; color: #666;">Backup completion alerts</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Dark Mode</div>
                            <div style="font-size: 14px; color: #666;">Use dark theme</div>
                        </div>
                        <div class="switch" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">
                        <span>ℹ️</span>
                        <span>Support</span>
                    </div>
                    <button class="button secondary" style="margin-bottom: 8px;">Contact Support</button>
                    <button class="button secondary" style="margin-bottom: 8px;">View Help Center</button>
                    <button class="button secondary" style="margin-bottom: 8px;">Privacy Policy</button>
                    <button class="button secondary" style="color: #d0021b; border-color: #d0021b;">Sign Out</button>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar" id="tab-bar" style="display: none;">
                <button class="tab active" onclick="showTab('dashboard')">
                    <div class="tab-icon">📊</div>
                    <div>Dashboard</div>
                </button>
                <button class="tab" onclick="showTab('backup')">
                    <div class="tab-icon">💾</div>
                    <div>Backup</div>
                </button>
                <button class="tab" onclick="showTab('settings')">
                    <div class="tab-icon">⚙️</div>
                    <div>Settings</div>
                </button>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="info-panel">
            <div style="font-size: 24px; font-weight: bold; color: #333; margin-bottom: 20px; text-align: center;">
                🎯 SafeKeep Complete Demo
            </div>

            <div class="demo-controls">
                <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px;">
                    📱 Navigate the App
                </div>
                <button class="demo-btn active" onclick="showScreen('welcome-screen')">Welcome</button>
                <button class="demo-btn" onclick="showScreen('auth-screen')">Authentication</button>
                <button class="demo-btn" onclick="showScreen('onboarding-screen')">Onboarding</button>
                <button class="demo-btn" onclick="showScreen('subscription-screen')">Subscription</button>
                <button class="demo-btn" onclick="showMainApp()">Main App</button>
            </div>

            <div style="margin-bottom: 25px;">
                <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px;">
                    ✨ Key Features Demo
                </div>
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                        <span>Complete User Flow</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                        <span>Interactive Backup System</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                        <span>Real-time Progress Tracking</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                        <span>Modular Pricing System</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                        <span>Security & Encryption</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0;">
                        <span>Settings & Configuration</span>
                        <span style="color: #50e3c2;">✅</span>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 25px;">
                <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px;">
                    🚀 Demo Actions
                </div>
                <button class="demo-btn" onclick="simulateBackup()">Simulate Backup</button>
                <button class="demo-btn" onclick="updateStats()">Update Statistics</button>
                <button class="demo-btn" onclick="showNotification('Backup completed successfully!')">Show Notification</button>
                <button class="demo-btn" onclick="resetDemo()">Reset Demo</button>
            </div>

            <div style="background: rgba(74, 144, 226, 0.1); padding: 15px; border-radius: 8px;">
                <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 8px;">
                    💡 About This Demo
                </div>
                <div style="font-size: 14px; color: #666; line-height: 1.6;">
                    This interactive demo showcases the complete SafeKeep app experience, from initial onboarding
                    through daily usage. All features are functional including backup simulation, progress tracking,
                    and settings management. The app demonstrates military-grade security with AES-256 encryption
                    and Supabase cloud integration.
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentScreen = 'welcome-screen';
        let isMainApp = false;
        let backupProgress = {
            photos: 100,
            contacts: 100,
            messages: 100
        };

        // Screen navigation
        function showScreen(screenId) {
            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // Show target screen
            document.getElementById(screenId).classList.add('active');
            currentScreen = screenId;

            // Update header title
            const titles = {
                'welcome-screen': 'SafeKeep',
                'auth-screen': 'Sign In',
                'onboarding-screen': 'Setup',
                'subscription-screen': 'Choose Plan',
                'dashboard-screen': 'Dashboard',
                'backup-screen': 'Backup',
                'settings-screen': 'Settings'
            };
            document.getElementById('screen-title').textContent = titles[screenId] || 'SafeKeep';

            // Update demo buttons
            document.querySelectorAll('.demo-btn').forEach(btn => btn.classList.remove('active'));
            if (event?.target) event.target.classList.add('active');

            // Show/hide tab bar for main app
            const tabBar = document.getElementById('tab-bar');
            if (['dashboard-screen', 'backup-screen', 'settings-screen'].includes(screenId)) {
                tabBar.style.display = 'flex';
                isMainApp = true;
            } else {
                tabBar.style.display = 'none';
                isMainApp = false;
            }
        }

        // Tab navigation
        function showTab(tabName) {
            const screenMap = {
                'dashboard': 'dashboard-screen',
                'backup': 'backup-screen',
                'settings': 'settings-screen'
            };

            showScreen(screenMap[tabName]);

            // Update active tab
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.closest('.tab').classList.add('active');
        }

        // Show main app (dashboard)
        function showMainApp() {
            showScreen('dashboard-screen');
            document.getElementById('tab-bar').style.display = 'flex';
            isMainApp = true;
        }

        // Back navigation
        function goBack() {
            if (isMainApp) {
                showScreen('subscription-screen');
            } else {
                const backFlow = {
                    'auth-screen': 'welcome-screen',
                    'onboarding-screen': 'auth-screen',
                    'subscription-screen': 'onboarding-screen'
                };
                if (backFlow[currentScreen]) {
                    showScreen(backFlow[currentScreen]);
                }
            }
        }

        // Permission granting
        function grantPermission(type) {
            const button = event.target;
            button.textContent = '✓ Granted';
            button.style.background = '#50e3c2';
            button.style.borderColor = '#50e3c2';
            button.style.color = 'white';
            button.disabled = true;

            // Update progress
            const progressBar = document.querySelector('#onboarding-screen .progress-fill');
            if (progressBar) {
                progressBar.style.width = '100%';
            }

            showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} permission granted!`);
        }

        // Plan selection
        function selectPlan(planType) {
            showNotification(`${planType.charAt(0).toUpperCase() + planType.slice(1)} plan selected!`);
            setTimeout(() => {
                showMainApp();
            }, 1000);
        }

        // Toggle switches
        function toggleSwitch(element) {
            element.classList.toggle('on');
            const isOn = element.classList.contains('on');
            showNotification(`Setting ${isOn ? 'enabled' : 'disabled'}`);
        }

        // Backup functions
        function startFullBackup() {
            showNotification('Starting full backup...');
            simulateBackup();
        }

        function startPhotoBackup() {
            showNotification('Starting photo backup...');
            simulateProgress('photos');
        }

        function startContactBackup() {
            showNotification('Starting contact sync...');
            simulateProgress('contacts');
        }

        function startMessageBackup() {
            showNotification('Starting message backup...');
            simulateProgress('messages');
        }

        // Simulate backup progress
        function simulateBackup() {
            showScreen('backup-screen');

            ['photos', 'contacts', 'messages'].forEach((type, index) => {
                setTimeout(() => {
                    simulateProgress(type);
                }, index * 2000);
            });
        }

        function simulateProgress(type) {
            const progressBars = document.querySelectorAll(`#backup-screen .progress-fill`);
            const statusIndicators = document.querySelectorAll(`#backup-screen .status-indicator`);

            // Reset progress
            backupProgress[type] = 0;

            const interval = setInterval(() => {
                backupProgress[type] += 10;

                // Update progress bars (simplified - would need proper targeting in real implementation)
                progressBars.forEach(bar => {
                    bar.style.width = backupProgress[type] + '%';
                });

                if (backupProgress[type] >= 100) {
                    clearInterval(interval);
                    showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} backup completed!`);

                    // Update status indicators
                    statusIndicators.forEach(indicator => {
                        indicator.className = 'status-indicator status-success';
                    });
                }
            }, 200);
        }

        // Update statistics
        function updateStats() {
            const photosCount = document.getElementById('photos-count');
            const contactsCount = document.getElementById('contacts-count');
            const messagesCount = document.getElementById('messages-count');

            if (photosCount) photosCount.textContent = (parseInt(photosCount.textContent.replace(',', '')) + Math.floor(Math.random() * 50)).toLocaleString();
            if (contactsCount) contactsCount.textContent = parseInt(contactsCount.textContent) + Math.floor(Math.random() * 5);
            if (messagesCount) messagesCount.textContent = (parseInt(messagesCount.textContent.replace(',', '')) + Math.floor(Math.random() * 100)).toLocaleString();

            showNotification('Statistics updated!');
        }

        // Show notification
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4A90E2;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                font-weight: 600;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Reset demo
        function resetDemo() {
            showScreen('welcome-screen');
            backupProgress = { photos: 100, contacts: 100, messages: 100 };

            // Reset all progress bars
            document.querySelectorAll('.progress-fill').forEach(bar => {
                bar.style.width = '100%';
            });

            // Reset all switches
            document.querySelectorAll('.switch').forEach(switchEl => {
                switchEl.classList.remove('on');
            });

            // Reset some switches to default on state
            document.querySelectorAll('#settings-screen .switch').forEach((switchEl, index) => {
                if ([0, 1, 4, 5].includes(index)) { // Auto backup, WiFi only, Biometric, Notifications
                    switchEl.classList.add('on');
                }
            });

            showNotification('Demo reset to beginning!');
        }

        // Show backup history
        function showBackupHistory() {
            showNotification('Backup history feature coming soon!');
        }

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state
            showScreen('welcome-screen');

            // Auto-advance demo after 5 seconds if on welcome screen
            setTimeout(() => {
                if (currentScreen === 'welcome-screen') {
                    showNotification('👋 Welcome! Click the navigation buttons to explore the app');
                }
            }, 2000);
        });
    </script>
</body>
</html>
