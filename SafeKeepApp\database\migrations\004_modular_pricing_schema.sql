-- SafeKeep Modular Pricing Schema Migration
-- Migration 004: Complete modular pricing structure with backward compatibility
-- Run this script in your Supabase SQL Editor

-- ============================================================================
-- STEP 1: Create new tables for modular pricing structure
-- ============================================================================

-- Create service_types table to define available backup services
CREATE TABLE IF NOT EXISTS public.service_types (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    base_price_cents INTEGER NOT NULL DEFAULT 0,
    storage_limit_gb INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_plans table for the new modular structure
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_cents INTEGER NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    billing_interval VARCHAR(20) NOT NULL DEFAULT 'month',
    total_storage_gb INTEGER NOT NULL DEFAULT 1,
    is_combination BOOLEAN NOT NULL DEFAULT FALSE,
    is_popular BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    discount_percentage INTEGER DEFAULT 0,
    savings_cents INTEGER DEFAULT 0,
    stripe_product_id VARCHAR(100),
    stripe_price_id VARCHAR(100),
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create plan_services junction table to define which services are included in each plan
CREATE TABLE IF NOT EXISTS public.plan_services (
    id SERIAL PRIMARY KEY,
    plan_id VARCHAR(50) NOT NULL,
    service_type_id VARCHAR(50) NOT NULL,
    is_included BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (service_type_id) REFERENCES public.service_types(id) ON DELETE CASCADE,
    UNIQUE(plan_id, service_type_id)
);

-- Create user_service_selections table to track which services each user has selected
CREATE TABLE IF NOT EXISTS public.user_service_selections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    service_type_id VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    activated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deactivated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (service_type_id) REFERENCES public.service_types(id) ON DELETE CASCADE,
    UNIQUE(user_id, service_type_id)
);

-- Create user_subscriptions table (new comprehensive structure)
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    plan_id VARCHAR(50),
    legacy_tier_id VARCHAR(50), -- For backward compatibility
    
    -- Subscription details
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete')),
    total_price_cents INTEGER DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    billing_interval VARCHAR(20) NOT NULL DEFAULT 'month',
    service_combination JSONB, -- Array of selected services
    
    -- Stripe integration
    stripe_customer_id VARCHAR(100),
    stripe_subscription_id VARCHAR(100),
    stripe_payment_method_id VARCHAR(100),
    
    -- Billing periods
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id),
    UNIQUE(user_id) -- One active subscription per user
);

-- ============================================================================
-- STEP 2: Add new columns to existing tables for enhanced tracking
-- ============================================================================

-- Add service-specific usage tracking columns to storage_usage table
ALTER TABLE public.storage_usage 
ADD COLUMN IF NOT EXISTS contacts_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS messages_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS photos_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS contacts_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS messages_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS photos_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_service_sync JSONB DEFAULT '{}'::jsonb; -- Track last sync per service

-- Add modular pricing support to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS selected_services JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS subscription_plan_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS total_subscription_cost_cents INTEGER DEFAULT 0;

-- ============================================================================
-- STEP 3: Create indexes for performance
-- ============================================================================

-- Service types indexes
CREATE INDEX IF NOT EXISTS idx_service_types_active ON public.service_types(is_active);
CREATE INDEX IF NOT EXISTS idx_service_types_sort_order ON public.service_types(sort_order);

-- Subscription plans indexes
CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON public.subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_combination ON public.subscription_plans(is_combination);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_popular ON public.subscription_plans(is_popular);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_price ON public.subscription_plans(price_cents);

-- Plan services indexes
CREATE INDEX IF NOT EXISTS idx_plan_services_plan_id ON public.plan_services(plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_services_service_type_id ON public.plan_services(service_type_id);

-- User service selections indexes
CREATE INDEX IF NOT EXISTS idx_user_service_selections_user_id ON public.user_service_selections(user_id);
CREATE INDEX IF NOT EXISTS idx_user_service_selections_service_type_id ON public.user_service_selections(service_type_id);
CREATE INDEX IF NOT EXISTS idx_user_service_selections_active ON public.user_service_selections(is_active);

-- User subscriptions indexes
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan_id ON public.user_subscriptions(plan_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_customer_id ON public.user_subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_subscription_id ON public.user_subscriptions(stripe_subscription_id);

-- Storage usage service-specific indexes
CREATE INDEX IF NOT EXISTS idx_storage_usage_contacts_count ON public.storage_usage(contacts_count);
CREATE INDEX IF NOT EXISTS idx_storage_usage_messages_count ON public.storage_usage(messages_count);
CREATE INDEX IF NOT EXISTS idx_storage_usage_photos_count ON public.storage_usage(photos_count);

-- Users modular pricing indexes
CREATE INDEX IF NOT EXISTS idx_users_subscription_plan_id ON public.users(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_users_selected_services ON public.users USING GIN(selected_services);

-- ============================================================================
-- STEP 4: Add table comments for documentation
-- ============================================================================

COMMENT ON TABLE public.service_types IS 'Defines available backup services (contacts, messages, photos) with pricing and storage limits';
COMMENT ON TABLE public.subscription_plans IS 'Modular subscription plans including individual services and combination packages';
COMMENT ON TABLE public.plan_services IS 'Junction table linking subscription plans to included services';
COMMENT ON TABLE public.user_service_selections IS 'Tracks which backup services each user has activated';
COMMENT ON TABLE public.user_subscriptions IS 'Comprehensive user subscription management with Stripe integration';

COMMENT ON COLUMN public.subscription_plans.is_combination IS 'TRUE for multi-service plans, FALSE for individual service plans';
COMMENT ON COLUMN public.subscription_plans.discount_percentage IS 'Percentage discount for combination plans';
COMMENT ON COLUMN public.subscription_plans.savings_cents IS 'Total savings in cents compared to individual services';
COMMENT ON COLUMN public.user_subscriptions.service_combination IS 'JSON array of selected service IDs';
COMMENT ON COLUMN public.users.selected_services IS 'JSON array of currently selected service IDs';
COMMENT ON COLUMN public.storage_usage.last_service_sync IS 'JSON object tracking last sync timestamp per service';
