# SafeKeep Modular Pricing API Documentation

## Overview

The SafeKeep API supports a flexible modular pricing system where users can select individual services (contacts, messages, photos) or benefit from automatic savings with combination plans. All endpoints require authentication via Bear<PERSON> token.

## Base URL
```
https://api.safekeep.com/api
```

## Authentication
```
Authorization: Bearer <jwt-token>
```

## 🎯 Pricing Calculation Endpoints

### Calculate Optimal Pricing
Calculate the best pricing plan for a given service combination with automatic savings detection.

**POST** `/pricing/calculate`

**Request Body:**
```json
{
  "serviceIds": ["contacts", "messages", "photos"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendedPlanId": "complete-backup",
    "recommendedPlanName": "Complete Backup",
    "priceCents": 699,
    "savingsCents": 98,
    "individualTotalCents": 797,
    "totalStorageGb": 13,
    "savingsPercentage": 12.3,
    "services": ["contacts", "messages", "photos"]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Bulk Pricing Calculation
Calculate pricing for multiple service combinations simultaneously.

**GET** `/pricing/bulk?combinations=[["contacts"],["messages"],["contacts","photos"]]`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "serviceIds": ["contacts"],
      "recommendedPlanId": "contacts-only",
      "priceCents": 99,
      "savingsCents": 0,
      "totalStorageGb": 1
    },
    {
      "serviceIds": ["contacts", "photos"],
      "recommendedPlanId": "contacts-photos",
      "priceCents": 549,
      "savingsCents": 49,
      "totalStorageGb": 11
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get Service Pricing
Get base pricing for all individual services.

**GET** `/pricing/services`

**Response:**
```json
{
  "success": true,
  "data": {
    "contacts": {
      "priceCents": 99,
      "storageGb": 1,
      "features": ["Contact deduplication", "Cross-device sync", "Backup history"]
    },
    "messages": {
      "priceCents": 199,
      "storageGb": 2,
      "features": ["SMS & MMS backup", "Message threading", "Search functionality"]
    },
    "photos": {
      "priceCents": 499,
      "storageGb": 10,
      "features": ["Automatic backup", "Smart compression", "Album organization"]
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 📋 Subscription Management Endpoints

### Create Subscription
Create a new subscription with selected services and automatic plan optimization.

**POST** `/subscriptions`

**Request Body:**
```json
{
  "userId": "user-uuid-here",
  "serviceIds": ["contacts", "messages"],
  "paymentMethodId": "pm_stripe_payment_method_id",
  "billingEmail": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "sub-uuid",
    "planId": "contacts-messages",
    "planName": "Contacts + Messages",
    "serviceIds": ["contacts", "messages"],
    "totalPriceCents": 249,
    "savingsCents": 49,
    "status": "active",
    "stripeSubscriptionId": "sub_stripe_id",
    "currentPeriodStart": "2024-01-15T00:00:00Z",
    "currentPeriodEnd": "2024-02-15T00:00:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Update Subscription
Update an existing subscription with new services, including proration handling.

**PUT** `/subscriptions/{subscriptionId}`

**Request Body:**
```json
{
  "serviceIds": ["contacts", "messages", "photos"],
  "prorationBehavior": "create_prorations"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "sub-uuid",
    "planId": "complete-backup",
    "planName": "Complete Backup",
    "serviceIds": ["contacts", "messages", "photos"],
    "totalPriceCents": 699,
    "savingsCents": 98,
    "status": "active",
    "prorationAmount": 450,
    "nextBillingDate": "2024-02-15T00:00:00Z"
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### Get Subscription Details
Get detailed subscription information for a user.

**GET** `/subscriptions/{userId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "sub-uuid",
    "planId": "complete-backup",
    "planName": "Complete Backup",
    "currentPriceCents": 699,
    "status": "active",
    "services": ["contacts", "messages", "photos"],
    "storageQuotaGb": 13,
    "storageUsedGb": 2.5,
    "nextBillingDate": "2024-02-15T00:00:00Z",
    "savingsPerMonth": 98
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Cancel Subscription
Cancel an active subscription (cancels at period end).

**DELETE** `/subscriptions/{subscriptionId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "cancelled": true,
    "cancelAtPeriodEnd": true,
    "currentPeriodEnd": "2024-02-15T00:00:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔐 Service Access Validation Endpoints

### Check Service Access
Validate if a user has access to a specific service.

**GET** `/subscriptions/{userId}/access/{serviceId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "hasAccess": true,
    "serviceId": "photos",
    "reason": null,
    "upgradeRequired": false
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Access Denied Response:**
```json
{
  "success": true,
  "data": {
    "hasAccess": false,
    "serviceId": "photos",
    "reason": "service_not_included",
    "upgradeRequired": true,
    "suggestedPlan": "messages-photos",
    "currentPlan": "messages-only"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get User Services
Get all services a user has access to.

**GET** `/subscriptions/{userId}/services`

**Response:**
```json
{
  "success": true,
  "data": {
    "services": ["contacts", "messages", "photos"],
    "planId": "complete-backup",
    "planName": "Complete Backup",
    "totalStorageGb": 13,
    "usedStorageGb": 2.5
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Validate Feature Access
Check if user can access a specific feature before performing operations.

**POST** `/subscriptions/{userId}/validate`

**Request Body:**
```json
{
  "serviceId": "photos",
  "operation": "backup_photos",
  "showPrompt": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "canAccess": true,
    "serviceId": "photos",
    "operation": "backup_photos"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 💳 Billing Integration Endpoints

### Create Payment Intent
Create a payment intent for subscription or upgrade.

**POST** `/billing/payment-intent`

**Request Body:**
```json
{
  "amount": 699,
  "currency": "usd",
  "serviceIds": ["contacts", "messages", "photos"],
  "userId": "user-uuid",
  "planId": "complete-backup",
  "description": "SafeKeep Complete Backup Subscription"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "clientSecret": "pi_xxx_secret_xxx",
    "paymentIntentId": "pi_xxx",
    "amount": 699,
    "currency": "usd"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Process Webhook
Handle Stripe webhooks for subscription events.

**POST** `/billing/webhook`

**Headers:**
```
Stripe-Signature: t=xxx,v1=xxx
```

**Response:**
```json
{
  "success": true,
  "data": {
    "received": true,
    "eventType": "customer.subscription.updated"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🚨 Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "specific field that caused error",
      "value": "invalid value"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `INVALID_INPUT` | Invalid or missing request parameters | 400 |
| `INVALID_SERVICE_COMBINATION` | Invalid service combination provided | 400 |
| `SUBSCRIPTION_NOT_FOUND` | Subscription not found for user | 404 |
| `SUBSCRIPTION_EXISTS` | User already has an active subscription | 409 |
| `PRICING_CALCULATION_ERROR` | Error calculating pricing | 500 |
| `SERVICE_ACCESS_DENIED` | User doesn't have access to service | 403 |
| `PAYMENT_FAILED` | Payment processing failed | 402 |
| `AUTHENTICATION_ERROR` | Invalid or missing authentication | 401 |

## 📊 Service Types Reference

| Service ID | Name | Price (cents) | Storage (GB) | Features |
|------------|------|---------------|--------------|----------|
| `contacts` | Contacts Backup | 99 | 1 | Deduplication, Sync, History |
| `messages` | Messages Backup | 199 | 2 | SMS/MMS, Threading, Search |
| `photos` | Photos Backup | 499 | 10 | Auto backup, Compression, Organization |

## 💰 Subscription Plans Reference

| Plan ID | Name | Services | Price (cents) | Savings (cents) |
|---------|------|----------|---------------|-----------------|
| `contacts-only` | Contacts Only | contacts | 99 | 0 |
| `messages-only` | Messages Only | messages | 199 | 0 |
| `photos-only` | Photos Only | photos | 499 | 0 |
| `contacts-messages` | Contacts + Messages | contacts, messages | 249 | 49 |
| `contacts-photos` | Contacts + Photos | contacts, photos | 549 | 49 |
| `messages-photos` | Messages + Photos | messages, photos | 649 | 49 |
| `complete-backup` | Complete Backup | contacts, messages, photos | 699 | 98 |

## 🔧 Rate Limits

- **Pricing calculations**: 100 requests per minute per user
- **Subscription operations**: 10 requests per minute per user
- **Service access checks**: 200 requests per minute per user
- **Billing operations**: 20 requests per minute per user

## 📱 SDK Integration Examples

### JavaScript/React Native
```javascript
// Calculate pricing
const response = await fetch('/api/pricing/calculate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    serviceIds: ['contacts', 'messages']
  })
});

// Check service access
const hasAccess = await fetch(`/api/subscriptions/${userId}/access/photos`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

This API provides comprehensive support for SafeKeep's modular pricing system with automatic optimization, service validation, and flexible subscription management.
