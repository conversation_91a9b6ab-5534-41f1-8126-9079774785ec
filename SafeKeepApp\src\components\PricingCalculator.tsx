import React from 'react';
import {
  View,
  StyleSheet,
  Animated,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';

export interface PricingResult {
  totalPrice: number;
  individualTotal: number;
  savings: number;
  recommendedPlan?: {
    id: string;
    name: string;
    isPopular: boolean;
  };
  totalStorage: string;
}

interface PricingCalculatorProps {
  selectedServices: string[];
  pricingResult: PricingResult;
  serviceNames: { [key: string]: string };
  animated?: boolean;
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({
  selectedServices,
  pricingResult,
  serviceNames,
  animated = true,
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (animated && selectedServices.length > 0) {
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      animatedValue.setValue(selectedServices.length > 0 ? 1 : 0);
    }
  }, [selectedServices.length, animated, animatedValue]);

  if (selectedServices.length === 0) {
    return (
      <Card style={styles.emptyCard}>
        <Card.Content style={styles.emptyContent}>
          <Icon name="calculator" size={48} color={COLORS.textSecondary} />
          <Text style={styles.emptyTitle}>Select Services</Text>
          <Text style={styles.emptySubtitle}>
            Choose the backup services you need and see your pricing automatically calculated with potential savings
          </Text>
        </Card.Content>
      </Card>
    );
  }

  const animatedStyle = animated ? {
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.9, 1],
        }),
      },
    ],
    opacity: animatedValue,
  } : {};

  return (
    <Animated.View style={animatedStyle}>
      <Card style={[styles.pricingCard, pricingResult.savings > 0 && styles.savingsCard]}>
        <Card.Content style={styles.pricingContent}>
          {/* Recommended Plan Header */}
          {pricingResult.recommendedPlan && (
            <View style={styles.recommendedPlanHeader}>
              <Chip 
                style={[
                  styles.planChip,
                  pricingResult.recommendedPlan.isPopular && styles.popularPlanChip
                ]}
                textStyle={styles.planChipText}
                icon={pricingResult.recommendedPlan.isPopular ? "fire" : "lightbulb-outline"}
              >
                {pricingResult.recommendedPlan.isPopular ? 'Most Popular' : 'Recommended'}
              </Chip>
              <Text style={styles.recommendedPlanName}>
                {pricingResult.recommendedPlan.name}
              </Text>
            </View>
          )}

          {/* Main Pricing Display */}
          <View style={styles.mainPricing}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Total</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.totalPrice}>
                  ${(pricingResult.totalPrice / 100).toFixed(2)}
                </Text>
                <Text style={styles.pricePeriod}>/month</Text>
              </View>
            </View>

            {/* Savings Display */}
            {pricingResult.savings > 0 && (
              <View style={styles.savingsContainer}>
                <View style={styles.savingsRow}>
                  <Icon name="trending-down" size={16} color={COLORS.success} />
                  <Text style={styles.savingsLabel}>You save</Text>
                  <Text style={styles.savingsAmount}>
                    ${(pricingResult.savings / 100).toFixed(2)}/month
                  </Text>
                </View>
                <View style={styles.savingsHighlight}>
                  <Text style={styles.savingsText}>
                    💰 Save ${(pricingResult.savings / 100).toFixed(2)} compared to individual services
                  </Text>
                </View>
              </View>
            )}

            {/* Storage Information */}
            <View style={styles.storageRow}>
              <Icon name="cloud-outline" size={16} color={COLORS.textSecondary} />
              <Text style={styles.storageText}>{pricingResult.totalStorage} total storage</Text>
            </View>
          </View>

          {/* Selected Services List */}
          <View style={styles.selectedServicesSection}>
            <Text style={styles.selectedServicesTitle}>Included services:</Text>
            <View style={styles.selectedServicesList}>
              {selectedServices.map(serviceId => (
                <View key={serviceId} style={styles.selectedServiceItem}>
                  <Icon name="check-circle" size={14} color={COLORS.success} />
                  <Text style={styles.selectedServiceText}>
                    {serviceNames[serviceId] || serviceId}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          {/* Price Breakdown (if savings) */}
          {pricingResult.savings > 0 && (
            <View style={styles.priceBreakdown}>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Individual services total:</Text>
                <Text style={styles.breakdownAmount}>
                  ${(pricingResult.individualTotal / 100).toFixed(2)}
                </Text>
              </View>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Bundle discount:</Text>
                <Text style={[styles.breakdownAmount, styles.discountAmount]}>
                  -${(pricingResult.savings / 100).toFixed(2)}
                </Text>
              </View>
              <View style={[styles.breakdownRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Your price:</Text>
                <Text style={styles.finalPrice}>
                  ${(pricingResult.totalPrice / 100).toFixed(2)}/month
                </Text>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  emptyCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    padding: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  pricingCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    borderWidth: 2,
    borderColor: COLORS.border,
  },
  savingsCard: {
    borderColor: COLORS.success,
    backgroundColor: 'rgba(76, 175, 80, 0.02)',
  },
  pricingContent: {
    padding: SPACING.lg,
  },
  recommendedPlanHeader: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  planChip: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  popularPlanChip: {
    backgroundColor: COLORS.accent,
  },
  planChipText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: FONTS.sizes.small,
  },
  recommendedPlanName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
  },
  mainPricing: {
    marginBottom: SPACING.lg,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  priceLabel: {
    fontSize: FONTS.sizes.large,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  totalPrice: {
    fontSize: FONTS.sizes.xxlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  pricePeriod: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  savingsContainer: {
    marginVertical: SPACING.md,
  },
  savingsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.sm,
  },
  savingsLabel: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.success,
    marginLeft: SPACING.xs,
    marginRight: SPACING.sm,
  },
  savingsAmount: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.success,
  },
  savingsHighlight: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: SPACING.sm,
    borderRadius: 8,
    alignItems: 'center',
  },
  savingsText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.success,
    fontWeight: '600',
  },
  storageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm,
  },
  storageText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  selectedServicesSection: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.md,
    marginBottom: SPACING.md,
  },
  selectedServicesTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  selectedServicesList: {
    gap: SPACING.xs,
  },
  selectedServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedServiceText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  priceBreakdown: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.md,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  breakdownLabel: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
  },
  breakdownAmount: {
    fontSize: FONTS.sizes.small,
    color: COLORS.text,
  },
  discountAmount: {
    color: COLORS.success,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  totalLabel: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
  },
  finalPrice: {
    fontSize: FONTS.sizes.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
});

export default PricingCalculator;
