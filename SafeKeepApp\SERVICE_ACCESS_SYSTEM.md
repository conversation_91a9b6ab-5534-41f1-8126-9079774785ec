# SafeKeep Service Access Control System

## Overview

The Service Access Control System provides comprehensive permission management for SafeKeep's modular services (Contacts, Messages, Photos). It ensures users can only access features they've subscribed to while providing intuitive upgrade prompts and seamless user experience.

## 🎯 Key Features

### ✅ **Service Permission Checks**
- Real-time access validation for contacts, messages, and photos
- Cached access status with automatic refresh
- Subscription status integration

### ✅ **User Experience**
- Automatic upgrade prompts when accessing restricted features
- Visual indicators for locked/unlocked services
- Seamless integration with existing UI components

### ✅ **Developer Experience**
- React hooks for easy integration
- Higher-order components for automatic access control
- Comprehensive error handling and fallbacks

## 📱 Core Components

### 1. **ServiceAccessService**
Central service for managing access permissions and subscription data.

```typescript
import { serviceAccessService } from '../services/ServiceAccessService';

// Check specific service access
const accessResult = await serviceAccessService.hasServiceAccess('photos');
if (accessResult.hasAccess) {
  // User has access to photos
} else {
  // Show upgrade prompt
  await serviceAccessService.showUpgradePrompt('photos');
}

// Get all service access
const allAccess = await serviceAccessService.getAllServiceAccess();
// Returns: { contacts: boolean, messages: boolean, photos: boolean }

// Validate operation before performing
const canBackup = await serviceAccessService.validateServiceOperation(
  'contacts', 
  'backup contacts',
  true // Show prompt if access denied
);
```

### 2. **ServiceAccessGuard Component**
Higher-order component that wraps features requiring specific service access.

```typescript
import ServiceAccessGuard from '../components/ServiceAccessGuard';

// Wrap any component that requires service access
<ServiceAccessGuard serviceId="photos">
  <PhotoBackupScreen />
</ServiceAccessGuard>

// With custom fallback
<ServiceAccessGuard 
  serviceId="contacts"
  fallbackComponent={<CustomUpgradePrompt />}
  showUpgradePrompt={false}
>
  <ContactsFeature />
</ServiceAccessGuard>
```

### 3. **React Hooks**
Convenient hooks for accessing service permissions in components.

```typescript
import { useServiceAccess, useSpecificServiceAccess } from '../hooks/useServiceAccess';

// General service access hook
const {
  serviceAccess,
  subscription,
  hasContactsAccess,
  hasMessagesAccess,
  hasPhotosAccess,
  validateOperation,
  refreshAccess
} = useServiceAccess();

// Specific service hook
const {
  hasAccess,
  accessResult,
  showUpgradePrompt,
  validateOperation
} = useSpecificServiceAccess('photos');
```

### 4. **SubscriptionStatusCard Component**
Displays current subscription status and service access.

```typescript
import SubscriptionStatusCard from '../components/SubscriptionStatusCard';

<SubscriptionStatusCard 
  onManageSubscription={() => navigation.navigate('SubscriptionManager')}
  showManageButton={true}
/>
```

## 🔧 Implementation Patterns

### **Pattern 1: Feature Access Validation**
Validate access before performing operations:

```typescript
const handleBackupPhotos = async () => {
  const canBackup = await validateOperation('photos', 'backup photos');
  if (canBackup) {
    // Perform backup
    await PhotoBackupService.startBackup();
  }
  // Upgrade prompt shown automatically if access denied
};
```

### **Pattern 2: Conditional UI Rendering**
Show different UI based on access status:

```typescript
const PhotosScreen = () => {
  const { hasPhotosAccess, isLoading } = useServiceAccess();

  if (isLoading) return <LoadingSpinner />;

  return (
    <View>
      {hasPhotosAccess ? (
        <PhotoBackupInterface />
      ) : (
        <UpgradePromptCard serviceId="photos" />
      )}
    </View>
  );
};
```

### **Pattern 3: Protected Component Wrapping**
Automatically protect entire screens or features:

```typescript
// In your navigation or screen component
const renderTabContent = () => {
  switch (selectedTab) {
    case 'photos':
      return (
        <ServiceAccessGuard serviceId="photos">
          <PhotoBackupScreen />
        </ServiceAccessGuard>
      );
    case 'contacts':
      return (
        <ServiceAccessGuard serviceId="contacts">
          <ContactBackupScreen />
        </ServiceAccessGuard>
      );
  }
};
```

### **Pattern 4: Custom Access Handling**
Handle access denial with custom logic:

```typescript
const handleRestrictedAction = async () => {
  const accessResult = await serviceAccessService.hasServiceAccess('messages');
  
  if (!accessResult.hasAccess) {
    // Custom handling based on reason
    switch (accessResult.reason) {
      case 'no_subscription':
        showCustomOnboardingFlow();
        break;
      case 'service_not_included':
        showUpgradeOptions();
        break;
      case 'subscription_expired':
        showRenewalPrompt();
        break;
    }
    return;
  }
  
  // Proceed with action
  performRestrictedAction();
};
```

## 🎨 UI Integration Examples

### **Tab Navigation with Access Indicators**
```typescript
const BackupScreen = () => {
  const { serviceAccess, isLoading } = useServiceAccess();

  const tabButtons = [
    {
      value: 'photos',
      label: serviceAccess?.photos ? 'Photos' : 'Photos 🔒',
      icon: 'camera',
      disabled: isLoading,
    },
    {
      value: 'contacts',
      label: serviceAccess?.contacts ? 'Contacts' : 'Contacts 🔒',
      icon: 'account-multiple',
      disabled: isLoading,
    }
  ];

  return (
    <SegmentedButtons
      value={selectedTab}
      onValueChange={setSelectedTab}
      buttons={tabButtons}
    />
  );
};
```

### **Settings Screen Integration**
```typescript
const SettingsScreen = () => {
  return (
    <ScrollView>
      {/* Subscription Status */}
      <SubscriptionStatusCard />
      
      {/* Other settings */}
      <SettingsSection title="Backup Settings">
        <ServiceAccessGuard serviceId="photos">
          <PhotoBackupSettings />
        </ServiceAccessGuard>
      </SettingsSection>
    </ScrollView>
  );
};
```

## 🔄 Access Status Flow

### **1. Initial Load**
```
App Start → ServiceAccessService.getAllServiceAccess() → Cache Results → Update UI
```

### **2. Feature Access Check**
```
User Action → validateServiceOperation() → Check Cache → Return Result or Show Prompt
```

### **3. Subscription Change**
```
Subscription Update → serviceAccessService.clearCache() → Refresh Access → Update UI
```

### **4. Upgrade Flow**
```
Access Denied → Show Upgrade Prompt → Navigate to Pricing → Complete Purchase → Refresh Access
```

## 🛡️ Security Considerations

### **Client-Side Validation**
- All access checks are performed client-side for UX
- Server-side validation still required for actual operations
- Access tokens should be validated on backend

### **Cache Management**
- Access status cached for 5 minutes to reduce API calls
- Cache cleared on subscription changes
- Fallback to "no access" on errors

### **Error Handling**
- Graceful degradation when service is unavailable
- Default to "no access" for security
- Comprehensive error logging

## 📊 Access Reasons

The system provides detailed reasons for access denial:

- **`no_subscription`**: User has no active subscription
- **`service_not_included`**: Service not in current plan
- **`subscription_expired`**: Subscription has expired
- **`subscription_cancelled`**: Subscription was cancelled

## 🚀 Best Practices

### **1. Always Validate Before Operations**
```typescript
// ✅ Good
const canBackup = await validateOperation('photos', 'backup');
if (canBackup) {
  await performBackup();
}

// ❌ Bad
await performBackup(); // No access check
```

### **2. Use Appropriate Components**
```typescript
// ✅ Good - For entire screens/features
<ServiceAccessGuard serviceId="photos">
  <PhotoScreen />
</ServiceAccessGuard>

// ✅ Good - For conditional rendering
{hasPhotosAccess && <PhotoFeature />}

// ❌ Bad - Mixing patterns unnecessarily
<ServiceAccessGuard serviceId="photos">
  {hasPhotosAccess && <PhotoFeature />}
</ServiceAccessGuard>
```

### **3. Handle Loading States**
```typescript
// ✅ Good
const { hasAccess, isLoading } = useSpecificServiceAccess('photos');

if (isLoading) return <LoadingSpinner />;
return hasAccess ? <Feature /> : <UpgradePrompt />;

// ❌ Bad
const { hasAccess } = useSpecificServiceAccess('photos');
return hasAccess ? <Feature /> : <UpgradePrompt />; // No loading state
```

### **4. Refresh Access After Changes**
```typescript
// ✅ Good
const handleSubscriptionUpdate = async () => {
  await updateSubscription();
  await refreshAccess(); // Refresh access status
  showSuccessMessage();
};
```

## 🧪 Testing

### **Mock Service Access**
```typescript
// In tests
jest.mock('../services/ServiceAccessService', () => ({
  serviceAccessService: {
    hasServiceAccess: jest.fn().mockResolvedValue({ hasAccess: true }),
    getAllServiceAccess: jest.fn().mockResolvedValue({
      contacts: true,
      messages: false,
      photos: true
    })
  }
}));
```

### **Test Access Scenarios**
- Test with no subscription
- Test with partial access
- Test with expired subscription
- Test upgrade flow
- Test error handling

This comprehensive service access system ensures a secure, user-friendly experience while maintaining clear separation between free and premium features.
