/**
 * Stripe Configuration for SafeKeep Web Demo
 * Centralized configuration for Stripe integration
 */

class StripeConfig {
    constructor() {
        // Stripe API Configuration
        this.config = {
            // Test keys for demo - replace with your actual keys
            publishableKey: 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            secretKey: 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            
            // Webhook configuration
            webhookSecret: 'whsec_demo_webhook_secret_for_safekeep_demo',
            webhookEndpoint: '/api/stripe/webhook',
            
            // API endpoints
            endpoints: {
                createPaymentIntent: '/api/stripe/create-payment-intent',
                createCustomer: '/api/stripe/create-customer',
                createSubscription: '/api/stripe/create-subscription',
                updateSubscription: '/api/stripe/update-subscription',
                cancelSubscription: '/api/stripe/cancel-subscription',
                addPaymentMethod: '/api/stripe/add-payment-method',
                removePaymentMethod: '/api/stripe/remove-payment-method',
                getCustomer: '/api/stripe/customer',
                getSubscription: '/api/stripe/subscription',
                getPaymentMethods: '/api/stripe/payment-methods'
            },
            
            // Currency and locale settings
            currency: 'usd',
            locale: 'en',
            
            // Demo mode settings
            demoMode: true,
            simulateNetworkDelay: true,
            networkDelayMs: 1000
        };
        
        // Subscription tier configuration - Modular Pricing Structure
        this.subscriptionTiers = {
            // Individual Services
            contacts_only: {
                id: 'contacts_only',
                name: 'Contacts Only',
                description: 'Secure backup for your contacts',
                price: 99, // $0.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_safekeep',
                stripePriceId: 'price_demo_contacts_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: false,
                    storageLimit: 1, // 1GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 1,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            messages_only: {
                id: 'messages_only',
                name: 'Messages Only',
                description: 'Secure backup for your messages',
                price: 199, // $1.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_messages_safekeep',
                stripePriceId: 'price_demo_messages_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 1, // 1GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 1,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            photos_only: {
                id: 'photos_only',
                name: 'Photos Only',
                description: 'Secure backup for your photos',
                price: 499, // $4.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_photos_safekeep',
                stripePriceId: 'price_demo_photos_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 10, // 10GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 10,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            // Combination Services
            contacts_messages: {
                id: 'contacts_messages',
                name: 'Contacts + Messages',
                description: 'Backup contacts and messages together',
                price: 249, // $2.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_messages_safekeep',
                stripePriceId: 'price_demo_contacts_messages_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 2, // 2GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 2,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            contacts_photos: {
                id: 'contacts_photos',
                name: 'Contacts + Photos',
                description: 'Backup contacts and photos together',
                price: 549, // $5.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_photos_safekeep',
                stripePriceId: 'price_demo_contacts_photos_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 11, // 11GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 11,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            messages_photos: {
                id: 'messages_photos',
                name: 'Messages + Photos',
                description: 'Backup messages and photos together',
                price: 649, // $6.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_messages_photos_safekeep',
                stripePriceId: 'price_demo_messages_photos_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 11, // 11GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 11,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            complete_backup: {
                id: 'complete_backup',
                name: 'Complete Backup',
                description: 'Full backup solution for all your data',
                price: 699, // $6.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_complete_safekeep',
                stripePriceId: 'price_demo_complete_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 12, // 12GB
                    backupFrequency: ['manual', 'daily', 'weekly'],
                    prioritySupport: true,
                    advancedEncryption: true,
                    multiDeviceSync: true,
                    backupHistory: 90, // days
                    restoreSpeed: 'priority',
                    maxBackupsPerMonth: -1, // unlimited
                    maxRestoresPerMonth: -1 // unlimited
                },
                limits: {
                    maxStorageGB: 12,
                    maxBackupsPerMonth: -1, // unlimited
                    maxRestoresPerMonth: -1, // unlimited
                    maxDevices: 6
                }
            }
        };
    }

    /**
     * Get subscription tier by ID
     */
    getTier(tierId) {
        return this.subscriptionTiers[tierId] || null;
    }

    /**
     * Get all subscription tiers
     */
    getAllTiers() {
        return Object.values(this.subscriptionTiers);
    }

    /**
     * Get Stripe configuration
     */
    getConfig() {
        return this.config;
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Reset to default configuration
     */
    resetConfig() {
        this.config = {
            publishableKey: 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            secretKey: 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            webhookSecret: 'whsec_demo_webhook_secret_for_safekeep_demo',
            demoMode: true
        };
        console.log('🔄 Stripe configuration reset to defaults');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.StripeConfig = StripeConfig;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StripeConfig;
}
