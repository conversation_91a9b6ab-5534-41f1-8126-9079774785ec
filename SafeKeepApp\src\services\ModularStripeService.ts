import { initStripe, useStripe, useConfirmPayment } from '@stripe/stripe-react-native';

// Environment configuration
const STRIPE_PUBLISHABLE_KEY = process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';

// Stripe Product IDs (generated from create-modular-products.js)
export const STRIPE_PRODUCTS = {
  CONTACTS_ONLY: 'prod_contacts_only_id',
  MESSAGES_ONLY: 'prod_messages_only_id',
  PHOTOS_ONLY: 'prod_photos_only_id',
  CONTACTS_MESSAGES: 'prod_contacts_messages_id',
  CONTACTS_PHOTOS: 'prod_contacts_photos_id',
  MESSAGES_PHOTOS: 'prod_messages_photos_id',
  COMPLETE_BACKUP: 'prod_complete_backup_id',
};

// Stripe Price IDs (generated from create-modular-products.js)
export const STRIPE_PRICES = {
  CONTACTS_ONLY: 'price_contacts_only_id',
  MESSAGES_ONLY: 'price_messages_only_id',
  PHOTOS_ONLY: 'price_photos_only_id',
  CONTACTS_MESSAGES: 'price_contacts_messages_id',
  CONTACTS_PHOTOS: 'price_contacts_photos_id',
  MESSAGES_PHOTOS: 'price_messages_photos_id',
  COMPLETE_BACKUP: 'price_complete_backup_id',
};

// Service definitions with Stripe integration
export const MODULAR_SERVICES = {
  contacts: {
    id: 'contacts',
    name: 'Contacts Backup',
    description: 'Secure backup and sync of your contacts',
    price: 99, // $0.99
    storage: '1GB',
    stripeProductId: STRIPE_PRODUCTS.CONTACTS_ONLY,
    stripePriceId: STRIPE_PRICES.CONTACTS_ONLY,
    features: [
      'Contact deduplication',
      'Cross-device sync',
      'Backup history',
      'Export options'
    ]
  },
  messages: {
    id: 'messages',
    name: 'Messages Backup',
    description: 'Secure backup and sync of your messages',
    price: 199, // $1.99
    storage: '2GB',
    stripeProductId: STRIPE_PRODUCTS.MESSAGES_ONLY,
    stripePriceId: STRIPE_PRICES.MESSAGES_ONLY,
    features: [
      'SMS & MMS backup',
      'Message threading',
      'Search functionality',
      'Media attachments'
    ]
  },
  photos: {
    id: 'photos',
    name: 'Photos Backup',
    description: 'Secure backup and sync of your photos',
    price: 499, // $4.99
    storage: '10GB',
    stripeProductId: STRIPE_PRODUCTS.PHOTOS_ONLY,
    stripePriceId: STRIPE_PRICES.PHOTOS_ONLY,
    features: [
      'Automatic photo backup',
      'Smart compression',
      'Album organization',
      'Face recognition'
    ]
  }
};

// Combination plans with Stripe integration
export const MODULAR_PLANS = {
  'contacts-messages': {
    id: 'contacts-messages',
    name: 'Contacts + Messages',
    description: 'Essential communication backup bundle',
    price: 249, // $2.49
    savings: 49,
    storage: '3GB',
    services: ['contacts', 'messages'],
    stripeProductId: STRIPE_PRODUCTS.CONTACTS_MESSAGES,
    stripePriceId: STRIPE_PRICES.CONTACTS_MESSAGES,
    isPopular: false
  },
  'contacts-photos': {
    id: 'contacts-photos',
    name: 'Contacts + Photos',
    description: 'Personal memories and connections bundle',
    price: 549, // $5.49
    savings: 49,
    storage: '11GB',
    services: ['contacts', 'photos'],
    stripeProductId: STRIPE_PRODUCTS.CONTACTS_PHOTOS,
    stripePriceId: STRIPE_PRICES.CONTACTS_PHOTOS,
    isPopular: false
  },
  'messages-photos': {
    id: 'messages-photos',
    name: 'Messages + Photos',
    description: 'Complete personal data bundle',
    price: 649, // $6.49
    savings: 49,
    storage: '12GB',
    services: ['messages', 'photos'],
    stripeProductId: STRIPE_PRODUCTS.MESSAGES_PHOTOS,
    stripePriceId: STRIPE_PRICES.MESSAGES_PHOTOS,
    isPopular: true
  },
  'complete-backup': {
    id: 'complete-backup',
    name: 'Complete Backup',
    description: 'All services included - best value!',
    price: 699, // $6.99
    savings: 98,
    storage: '13GB',
    services: ['contacts', 'messages', 'photos'],
    stripeProductId: STRIPE_PRODUCTS.COMPLETE_BACKUP,
    stripePriceId: STRIPE_PRICES.COMPLETE_BACKUP,
    isPopular: true
  }
};

// Interfaces
export interface ModularPaymentIntentData {
  amount: number;
  currency: string;
  serviceIds: string[];
  userId: string;
  planId?: string;
  planName?: string;
  customerId?: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface ModularSubscriptionRequest {
  userId: string;
  serviceIds: string[];
  planId: string;
  priceCents: number;
  customerId?: string;
  paymentMethodId?: string;
  billingEmail?: string;
}

export interface SubscriptionChangeRequest {
  subscriptionId: string;
  newServiceIds: string[];
  newPlanId: string;
  newPriceCents: number;
  prorationBehavior?: 'create_prorations' | 'none' | 'always_invoice';
}

export interface PricingCalculationResult {
  serviceIds: string[];
  individualTotal: number;
  recommendedPlan?: typeof MODULAR_PLANS[keyof typeof MODULAR_PLANS];
  optimizedPrice: number;
  savings: number;
  totalStorage: string;
}

// Main Modular Stripe Service
export class ModularStripeService {
  private static instance: ModularStripeService;
  private initialized = false;

  static getInstance(): ModularStripeService {
    if (!ModularStripeService.instance) {
      ModularStripeService.instance = new ModularStripeService();
    }
    return ModularStripeService.instance;
  }

  /**
   * Initialize Stripe for React Native
   */
  async initialize(): Promise<boolean> {
    if (this.initialized) return true;

    try {
      await initStripe({
        publishableKey: STRIPE_PUBLISHABLE_KEY,
        merchantIdentifier: 'merchant.com.safekeep.app',
        urlScheme: 'safekeep',
      });
      
      this.initialized = true;
      console.log('✅ Modular Stripe service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Stripe:', error);
      throw new Error(`Stripe initialization failed: ${error}`);
    }
  }

  /**
   * Calculate optimal pricing for service combination
   */
  calculateOptimalPricing(serviceIds: string[]): PricingCalculationResult {
    // Calculate individual service total
    const individualTotal = serviceIds.reduce((total, serviceId) => {
      const service = MODULAR_SERVICES[serviceId as keyof typeof MODULAR_SERVICES];
      return total + (service?.price || 0);
    }, 0);

    // Find matching combination plan
    const sortedServices = [...serviceIds].sort().join(',');
    const matchingPlan = Object.values(MODULAR_PLANS).find(plan => 
      plan.services.sort().join(',') === sortedServices
    );

    const optimizedPrice = matchingPlan?.price || individualTotal;
    const savings = individualTotal - optimizedPrice;

    // Calculate total storage
    const totalStorageGB = serviceIds.reduce((total, serviceId) => {
      const service = MODULAR_SERVICES[serviceId as keyof typeof MODULAR_SERVICES];
      return total + parseInt(service?.storage || '0');
    }, 0);

    return {
      serviceIds,
      individualTotal,
      recommendedPlan: matchingPlan,
      optimizedPrice,
      savings,
      totalStorage: `${totalStorageGB}GB`
    };
  }

  /**
   * Create payment intent for modular subscription
   */
  async createPaymentIntent(data: ModularPaymentIntentData): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/payments/create-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          currency: data.currency,
          serviceIds: data.serviceIds,
          userId: data.userId,
          planId: data.planId,
          planName: data.planName,
          customerId: data.customerId,
          description: data.description || `SafeKeep ${data.planName || 'Custom'} Subscription`,
          metadata: {
            ...data.metadata,
            services: data.serviceIds.join(','),
            plan_id: data.planId || 'custom',
            user_id: data.userId,
            type: 'modular_subscription'
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  /**
   * Create modular subscription
   */
  async createSubscription(request: ModularSubscriptionRequest): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/modular`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Update existing subscription with new services
   */
  async updateSubscription(request: SubscriptionChangeRequest): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/${request.subscriptionId}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceIds: request.newServiceIds,
          planId: request.newPlanId,
          priceCents: request.newPriceCents,
          prorationBehavior: request.prorationBehavior || 'create_prorations'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw error;
    }
  }

  /**
   * Get Stripe price ID for service combination
   */
  getStripePriceId(serviceIds: string[]): string | null {
    const sortedServices = [...serviceIds].sort().join('-');
    const plan = MODULAR_PLANS[sortedServices as keyof typeof MODULAR_PLANS];
    return plan?.stripePriceId || null;
  }

  /**
   * Get billing descriptor for service combination
   */
  getBillingDescriptor(serviceIds: string[]): string {
    if (serviceIds.length === 1) {
      const service = MODULAR_SERVICES[serviceIds[0] as keyof typeof MODULAR_SERVICES];
      return `SafeKeep ${service?.name || 'Service'}`;
    }

    const sortedServices = [...serviceIds].sort().join('-');
    const plan = MODULAR_PLANS[sortedServices as keyof typeof MODULAR_PLANS];
    return `SafeKeep ${plan?.name || 'Custom Plan'}`;
  }

  /**
   * Get all available services
   */
  getAllServices() {
    return MODULAR_SERVICES;
  }

  /**
   * Get all available plans
   */
  getAllPlans() {
    return MODULAR_PLANS;
  }

  /**
   * Get service by ID
   */
  getService(serviceId: string) {
    return MODULAR_SERVICES[serviceId as keyof typeof MODULAR_SERVICES];
  }

  /**
   * Get plan by ID
   */
  getPlan(planId: string) {
    return MODULAR_PLANS[planId as keyof typeof MODULAR_PLANS];
  }
}

// Export singleton instance
export const modularStripeService = ModularStripeService.getInstance();

// Export React Native Stripe hooks
export { useStripe, useConfirmPayment };

// Export default
export default modularStripeService;
