import Stripe from 'stripe';
import { supabase } from '../config/supabase';

// Stripe Price IDs for modular plans (from create-modular-products.js)
const STRIPE_PRICE_IDS = {
  'contacts-only': process.env.STRIPE_PRICE_CONTACTS_ONLY || 'price_contacts_only_id',
  'messages-only': process.env.STRIPE_PRICE_MESSAGES_ONLY || 'price_messages_only_id',
  'photos-only': process.env.STRIPE_PRICE_PHOTOS_ONLY || 'price_photos_only_id',
  'contacts-messages': process.env.STRIPE_PRICE_CONTACTS_MESSAGES || 'price_contacts_messages_id',
  'contacts-photos': process.env.STRIPE_PRICE_CONTACTS_PHOTOS || 'price_contacts_photos_id',
  'messages-photos': process.env.STRIPE_PRICE_MESSAGES_PHOTOS || 'price_messages_photos_id',
  'complete-backup': process.env.STRIPE_PRICE_COMPLETE_BACKUP || 'price_complete_backup_id',
};

// Service to plan mapping
const SERVICE_COMBINATIONS = {
  'contacts': 'contacts-only',
  'messages': 'messages-only',
  'photos': 'photos-only',
  'contacts,messages': 'contacts-messages',
  'contacts,photos': 'contacts-photos',
  'messages,photos': 'messages-photos',
  'contacts,messages,photos': 'complete-backup',
};

// Billing descriptors for each plan
const BILLING_DESCRIPTORS = {
  'contacts-only': 'SafeKeep Contacts',
  'messages-only': 'SafeKeep Messages',
  'photos-only': 'SafeKeep Photos',
  'contacts-messages': 'SafeKeep Contacts+Messages',
  'contacts-photos': 'SafeKeep Contacts+Photos',
  'messages-photos': 'SafeKeep Messages+Photos',
  'complete-backup': 'SafeKeep Complete',
};

export interface ModularSubscriptionRequest {
  userId: string;
  serviceIds: string[];
  planId: string;
  priceCents: number;
  customerId?: string;
  paymentMethodId?: string;
  billingEmail?: string;
}

export interface SubscriptionUpdateRequest {
  subscriptionId: string;
  newServiceIds: string[];
  newPlanId: string;
  newPriceCents: number;
  prorationBehavior?: 'create_prorations' | 'none' | 'always_invoice';
}

export interface PaymentIntentRequest {
  amount: number;
  currency: string;
  serviceIds: string[];
  userId: string;
  planId?: string;
  planName?: string;
  customerId?: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface BillingResult {
  success: boolean;
  data?: any;
  error?: string;
}

export class ModularBillingIntegration {
  private stripe: Stripe;

  constructor() {
    const secretKey = process.env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    
    this.stripe = new Stripe(secretKey, {
      apiVersion: '2023-10-16',
    });
  }

  /**
   * Create payment intent for modular subscription
   */
  async createPaymentIntent(request: PaymentIntentRequest): Promise<BillingResult> {
    try {
      const planId = this.getPlanIdFromServices(request.serviceIds);
      const billingDescriptor = BILLING_DESCRIPTORS[planId as keyof typeof BILLING_DESCRIPTORS];

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: request.amount,
        currency: request.currency,
        customer: request.customerId,
        description: request.description || `SafeKeep ${request.planName || 'Subscription'}`,
        statement_descriptor: billingDescriptor,
        metadata: {
          user_id: request.userId,
          service_ids: request.serviceIds.join(','),
          plan_id: planId,
          type: 'modular_subscription',
          ...request.metadata,
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        success: true,
        data: {
          client_secret: paymentIntent.client_secret,
          payment_intent_id: paymentIntent.id,
        },
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment intent',
      };
    }
  }

  /**
   * Create modular subscription
   */
  async createSubscription(request: ModularSubscriptionRequest): Promise<BillingResult> {
    try {
      let customerId = request.customerId;

      // Create customer if not provided
      if (!customerId) {
        const customer = await this.stripe.customers.create({
          email: request.billingEmail,
          metadata: {
            user_id: request.userId,
            service_ids: request.serviceIds.join(','),
            plan_id: request.planId,
          },
        });
        customerId = customer.id;
      }

      // Attach payment method if provided
      if (request.paymentMethodId) {
        await this.stripe.paymentMethods.attach(request.paymentMethodId, {
          customer: customerId,
        });

        await this.stripe.customers.update(customerId, {
          invoice_settings: {
            default_payment_method: request.paymentMethodId,
          },
        });
      }

      // Get Stripe price ID for the plan
      const stripePriceId = STRIPE_PRICE_IDS[request.planId as keyof typeof STRIPE_PRICE_IDS];
      if (!stripePriceId) {
        throw new Error(`No Stripe price ID found for plan: ${request.planId}`);
      }

      // Create subscription
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [
          {
            price: stripePriceId,
          },
        ],
        metadata: {
          user_id: request.userId,
          service_ids: request.serviceIds.join(','),
          plan_id: request.planId,
          type: 'modular_subscription',
        },
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent'],
      });

      // Store subscription in database
      await this.storeSubscriptionInDatabase({
        subscriptionId: subscription.id,
        userId: request.userId,
        planId: request.planId,
        serviceIds: request.serviceIds,
        priceCents: request.priceCents,
        customerId,
        status: subscription.status,
      });

      const invoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

      return {
        success: true,
        data: {
          subscription_id: subscription.id,
          customer_id: customerId,
          payment_intent_id: paymentIntent?.id,
          client_secret: paymentIntent?.client_secret,
          status: subscription.status,
        },
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create subscription',
      };
    }
  }

  /**
   * Update existing subscription with new services
   */
  async updateSubscription(request: SubscriptionUpdateRequest): Promise<BillingResult> {
    try {
      // Get current subscription
      const subscription = await this.stripe.subscriptions.retrieve(request.subscriptionId);
      
      // Get new Stripe price ID
      const newStripePriceId = STRIPE_PRICE_IDS[request.newPlanId as keyof typeof STRIPE_PRICE_IDS];
      if (!newStripePriceId) {
        throw new Error(`No Stripe price ID found for plan: ${request.newPlanId}`);
      }

      // Update subscription
      const updatedSubscription = await this.stripe.subscriptions.update(request.subscriptionId, {
        items: [
          {
            id: subscription.items.data[0].id,
            price: newStripePriceId,
          },
        ],
        proration_behavior: request.prorationBehavior || 'create_prorations',
        metadata: {
          ...subscription.metadata,
          service_ids: request.newServiceIds.join(','),
          plan_id: request.newPlanId,
          updated_at: new Date().toISOString(),
        },
      });

      // Update database record
      await this.updateSubscriptionInDatabase({
        subscriptionId: request.subscriptionId,
        planId: request.newPlanId,
        serviceIds: request.newServiceIds,
        priceCents: request.newPriceCents,
      });

      return {
        success: true,
        data: {
          subscription_id: updatedSubscription.id,
          status: updatedSubscription.status,
          current_period_end: updatedSubscription.current_period_end,
        },
      };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update subscription',
      };
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<BillingResult> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
        metadata: {
          cancelled_at: new Date().toISOString(),
        },
      });

      // Update database
      await supabase
        .from('user_subscriptions')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('stripe_subscription_id', subscriptionId);

      return {
        success: true,
        data: {
          subscription_id: subscription.id,
          cancel_at_period_end: subscription.cancel_at_period_end,
          current_period_end: subscription.current_period_end,
        },
      };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel subscription',
      };
    }
  }

  /**
   * Handle Stripe webhooks
   */
  async handleWebhook(event: Stripe.Event): Promise<BillingResult> {
    try {
      console.log(`Processing webhook: ${event.type}`);

      switch (event.type) {
        case 'customer.subscription.created':
          return await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        
        case 'customer.subscription.updated':
          return await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        
        case 'customer.subscription.deleted':
          return await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        
        case 'invoice.payment_succeeded':
          return await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        
        case 'invoice.payment_failed':
          return await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
        
        default:
          console.log(`Unhandled webhook event: ${event.type}`);
          return { success: true };
      }
    } catch (error) {
      console.error('Webhook processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Webhook processing failed',
      };
    }
  }

  /**
   * Get plan ID from service combination
   */
  private getPlanIdFromServices(serviceIds: string[]): string {
    const sortedServices = serviceIds.sort().join(',');
    return SERVICE_COMBINATIONS[sortedServices as keyof typeof SERVICE_COMBINATIONS] || 'custom';
  }

  /**
   * Store subscription in database
   */
  private async storeSubscriptionInDatabase(data: {
    subscriptionId: string;
    userId: string;
    planId: string;
    serviceIds: string[];
    priceCents: number;
    customerId: string;
    status: string;
  }) {
    const { error } = await supabase
      .from('user_subscriptions')
      .insert({
        id: data.subscriptionId,
        user_id: data.userId,
        plan_id: data.planId,
        total_price_cents: data.priceCents,
        status: data.status,
        stripe_subscription_id: data.subscriptionId,
        stripe_customer_id: data.customerId,
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error storing subscription in database:', error);
      throw new Error(`Database error: ${error.message}`);
    }

    // Store service selections
    const serviceSelections = data.serviceIds.map(serviceId => ({
      user_id: data.userId,
      service_type_id: serviceId,
      is_active: true,
      created_at: new Date().toISOString(),
    }));

    const { error: serviceError } = await supabase
      .from('user_service_selections')
      .insert(serviceSelections);

    if (serviceError) {
      console.error('Error storing service selections:', serviceError);
      throw new Error(`Service selection error: ${serviceError.message}`);
    }
  }

  /**
   * Update subscription in database
   */
  private async updateSubscriptionInDatabase(data: {
    subscriptionId: string;
    planId: string;
    serviceIds: string[];
    priceCents: number;
  }) {
    // Update subscription
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        plan_id: data.planId,
        total_price_cents: data.priceCents,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', data.subscriptionId);

    if (error) {
      console.error('Error updating subscription in database:', error);
      throw new Error(`Database error: ${error.message}`);
    }

    // Get user ID
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_subscription_id', data.subscriptionId)
      .single();

    if (subscription) {
      // Deactivate old service selections
      await supabase
        .from('user_service_selections')
        .update({ is_active: false })
        .eq('user_id', subscription.user_id);

      // Add new service selections
      const serviceSelections = data.serviceIds.map(serviceId => ({
        user_id: subscription.user_id,
        service_type_id: serviceId,
        is_active: true,
        created_at: new Date().toISOString(),
      }));

      await supabase
        .from('user_service_selections')
        .insert(serviceSelections);
    }
  }

  // Webhook handlers
  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<BillingResult> {
    console.log(`Subscription created: ${subscription.id}`);
    // Additional logic for subscription creation
    return { success: true };
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<BillingResult> {
    console.log(`Subscription updated: ${subscription.id}`);
    // Additional logic for subscription updates
    return { success: true };
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<BillingResult> {
    console.log(`Subscription deleted: ${subscription.id}`);
    
    // Update database
    await supabase
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscription.id);

    return { success: true };
  }

  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<BillingResult> {
    console.log(`Payment succeeded for invoice: ${invoice.id}`);
    // Additional logic for successful payments
    return { success: true };
  }

  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<BillingResult> {
    console.log(`Payment failed for invoice: ${invoice.id}`);
    // Additional logic for failed payments (e.g., send notification)
    return { success: true };
  }
}

export default ModularBillingIntegration;
