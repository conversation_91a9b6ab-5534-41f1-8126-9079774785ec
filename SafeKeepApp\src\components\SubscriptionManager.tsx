import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';
import ServiceSelector, { ServiceType } from './ServiceSelector';
import PricingCalculator, { PricingResult } from './PricingCalculator';
import { modularStripeService } from '../services/ModularStripeService';

interface CurrentSubscription {
  id: string;
  planId: string;
  planName: string;
  serviceIds: string[];
  priceCents: number;
  status: string;
  currentPeriodEnd: string;
}

interface SubscriptionManagerProps {
  currentSubscription: CurrentSubscription;
  availableServices: { [key: string]: ServiceType };
  onSubscriptionUpdated: (newSubscription: CurrentSubscription) => void;
  userToken: string;
}

const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({
  currentSubscription,
  availableServices,
  onSubscriptionUpdated,
  userToken,
}) => {
  const [selectedServices, setSelectedServices] = useState<string[]>(currentSubscription.serviceIds);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showChanges, setShowChanges] = useState(false);
  const [newPricing, setNewPricing] = useState<PricingResult | null>(null);

  // Calculate new pricing when services change
  useEffect(() => {
    if (selectedServices.length > 0) {
      const pricingResult = modularStripeService.calculateOptimalPricing(selectedServices);
      setNewPricing({
        totalPrice: pricingResult.optimizedPrice,
        individualTotal: pricingResult.individualTotal,
        savings: pricingResult.savings,
        recommendedPlan: pricingResult.recommendedPlan,
        totalStorage: pricingResult.totalStorage,
      });
    } else {
      setNewPricing(null);
    }

    // Show changes if selection differs from current subscription
    const hasChanges = JSON.stringify(selectedServices.sort()) !== JSON.stringify(currentSubscription.serviceIds.sort());
    setShowChanges(hasChanges);
  }, [selectedServices, currentSubscription.serviceIds]);

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  };

  const handleUpdateSubscription = async () => {
    if (!newPricing || selectedServices.length === 0) {
      Alert.alert('No Services Selected', 'Please select at least one service.');
      return;
    }

    const newPlanId = newPricing.recommendedPlan?.id || 'custom';
    
    Alert.alert(
      'Update Subscription',
      `Update your subscription to ${newPricing.recommendedPlan?.name || 'Custom Plan'}?\n\nNew price: $${(newPricing.totalPrice / 100).toFixed(2)}/month\n\nChanges will be prorated and take effect immediately.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Update', onPress: confirmUpdate },
      ]
    );
  };

  const confirmUpdate = async () => {
    if (!newPricing) return;

    setIsUpdating(true);

    try {
      const response = await fetch(`/api/billing/subscriptions/${currentSubscription.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
        },
        body: JSON.stringify({
          serviceIds: selectedServices,
          planId: newPricing.recommendedPlan?.id || 'custom',
          priceCents: newPricing.totalPrice,
          prorationBehavior: 'create_prorations',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to update subscription');
      }

      const updatedSubscription = await response.json();

      // Update local state
      const newSubscription: CurrentSubscription = {
        ...currentSubscription,
        planId: newPricing.recommendedPlan?.id || 'custom',
        planName: newPricing.recommendedPlan?.name || 'Custom Plan',
        serviceIds: selectedServices,
        priceCents: newPricing.totalPrice,
      };

      onSubscriptionUpdated(newSubscription);
      setShowChanges(false);

      Alert.alert(
        'Subscription Updated! ✅',
        `Your subscription has been updated to ${newPricing.recommendedPlan?.name || 'Custom Plan'}.\n\nNew services are now active and your next bill will reflect the changes.`
      );
    } catch (error) {
      console.error('Subscription update error:', error);
      Alert.alert(
        'Update Failed',
        error instanceof Error ? error.message : 'Failed to update subscription. Please try again.'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelChanges = () => {
    setSelectedServices(currentSubscription.serviceIds);
    setShowChanges(false);
  };

  const renderCurrentPlan = () => (
    <Card style={styles.currentPlanCard}>
      <Card.Content>
        <View style={styles.currentPlanHeader}>
          <Icon name="shield-check" size={24} color={COLORS.success} />
          <Text style={styles.currentPlanTitle}>Current Plan</Text>
        </View>
        
        <Text style={styles.currentPlanName}>{currentSubscription.planName}</Text>
        <Text style={styles.currentPlanPrice}>
          ${(currentSubscription.priceCents / 100).toFixed(2)}/month
        </Text>
        
        <View style={styles.currentServices}>
          <Text style={styles.currentServicesTitle}>Active Services:</Text>
          {currentSubscription.serviceIds.map(serviceId => (
            <View key={serviceId} style={styles.currentServiceItem}>
              <Icon name="check-circle" size={14} color={COLORS.success} />
              <Text style={styles.currentServiceText}>
                {availableServices[serviceId]?.name || serviceId}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.billingInfo}>
          <Text style={styles.billingText}>
            Next billing: {new Date(currentSubscription.currentPeriodEnd).toLocaleDateString()}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const renderServiceSelection = () => (
    <View style={styles.serviceSelection}>
      <Text style={styles.sectionTitle}>Modify Your Services</Text>
      <Text style={styles.sectionSubtitle}>
        Add or remove services. Changes will be prorated on your next bill.
      </Text>
      
      {Object.values(availableServices).map(service => (
        <ServiceSelector
          key={service.id}
          service={service}
          isSelected={selectedServices.includes(service.id)}
          onToggle={handleServiceToggle}
          compact={true}
        />
      ))}
    </View>
  );

  const renderChangesPreview = () => {
    if (!showChanges || !newPricing) return null;

    const priceDifference = newPricing.totalPrice - currentSubscription.priceCents;
    const isUpgrade = priceDifference > 0;

    return (
      <Card style={[styles.changesCard, isUpgrade ? styles.upgradeCard : styles.downgradeCard]}>
        <Card.Content>
          <View style={styles.changesHeader}>
            <Icon 
              name={isUpgrade ? "trending-up" : "trending-down"} 
              size={20} 
              color={isUpgrade ? COLORS.primary : COLORS.success} 
            />
            <Text style={styles.changesTitle}>
              {isUpgrade ? 'Upgrade' : 'Downgrade'} Preview
            </Text>
          </View>

          <View style={styles.changesSummary}>
            <Text style={styles.newPlanName}>
              {newPricing.recommendedPlan?.name || 'Custom Plan'}
            </Text>
            <Text style={styles.newPlanPrice}>
              ${(newPricing.totalPrice / 100).toFixed(2)}/month
            </Text>
            
            <View style={styles.priceDifference}>
              <Text style={[
                styles.priceDifferenceText,
                { color: isUpgrade ? COLORS.primary : COLORS.success }
              ]}>
                {isUpgrade ? '+' : ''}${(priceDifference / 100).toFixed(2)}/month
              </Text>
            </View>

            {newPricing.savings > 0 && (
              <Chip style={styles.savingsChip} textStyle={styles.savingsChipText}>
                Save ${(newPricing.savings / 100).toFixed(2)}/month
              </Chip>
            )}
          </View>

          <View style={styles.changesActions}>
            <Button
              mode="outlined"
              onPress={handleCancelChanges}
              style={styles.cancelButton}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleUpdateSubscription}
              style={styles.updateButton}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                `${isUpgrade ? 'Upgrade' : 'Downgrade'} Now`
              )}
            </Button>
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      {renderCurrentPlan()}
      {renderServiceSelection()}
      {renderChangesPreview()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.lg,
    gap: SPACING.lg,
  },
  currentPlanCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderWidth: 1,
    borderColor: COLORS.success,
  },
  currentPlanHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  currentPlanTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.success,
    marginLeft: SPACING.sm,
  },
  currentPlanName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  currentPlanPrice: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.md,
  },
  currentServices: {
    marginBottom: SPACING.md,
  },
  currentServicesTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  currentServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  currentServiceText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  billingInfo: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
  },
  billingText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
  },
  serviceSelection: {
    gap: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
  },
  sectionSubtitle: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  changesCard: {
    borderWidth: 2,
  },
  upgradeCard: {
    borderColor: COLORS.primary,
    backgroundColor: 'rgba(74, 144, 226, 0.05)',
  },
  downgradeCard: {
    borderColor: COLORS.success,
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
  },
  changesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  changesTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginLeft: SPACING.sm,
  },
  changesSummary: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  newPlanName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  newPlanPrice: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  priceDifference: {
    marginBottom: SPACING.sm,
  },
  priceDifferenceText: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
  },
  savingsChip: {
    backgroundColor: COLORS.success,
  },
  savingsChipText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  changesActions: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  updateButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
});

export default SubscriptionManager;
