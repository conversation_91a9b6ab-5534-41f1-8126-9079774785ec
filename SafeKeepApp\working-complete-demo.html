<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Complete App Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 375px 1fr;
            gap: 30px;
            align-items: start;
        }

        .phone {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            height: 812px;
            position: relative;
        }

        .header {
            background: #4A90E2;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .screen {
            height: calc(100% - 120px);
            overflow-y: auto;
            padding: 20px;
            display: none;
        }

        .screen.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 8px;
            transition: background 0.3s;
        }

        .button:hover {
            background: #357abd;
        }

        .button.secondary {
            background: none;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }

        .tabs {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: none;
            height: 60px;
        }

        .tabs.show {
            display: flex;
        }

        .tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #666;
            font-size: 12px;
            border: none;
            background: none;
            transition: color 0.3s;
        }

        .tab.active {
            color: #4A90E2;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4A90E2;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .controls {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .control-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            margin: 5px;
            cursor: pointer;
        }

        .control-btn:hover {
            background: #5a6268;
        }

        .control-btn.active {
            background: #4A90E2;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .switch {
            width: 50px;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.on {
            background: #4A90E2;
        }

        .switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }

        .switch.on::after {
            transform: translateX(20px);
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #50e3c2;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .phone {
                width: 100%;
                max-width: 375px;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Phone Demo -->
        <div class="phone">
            <div class="header">
                <button onclick="goBack()" style="background: none; border: none; color: white; font-size: 16px; cursor: pointer;">←</button>
                <h1 id="title">SafeKeep</h1>
                <div></div>
            </div>

            <!-- Welcome Screen -->
            <div id="welcome" class="screen active">
                <div style="text-align: center; padding: 40px 0;">
                    <div style="font-size: 80px; margin-bottom: 20px;">🛡️</div>
                    <h1 style="font-size: 32px; font-weight: bold; color: #333; margin-bottom: 8px;">SafeKeep</h1>
                    <p style="font-size: 18px; color: #666; margin-bottom: 32px;">Your Precious Data, Perfectly Protected</p>
                </div>

                <div class="card">
                    <p style="text-align: center; margin-bottom: 24px; color: #666; line-height: 1.5;">
                        Securely backup your photos, contacts, and messages with military-grade encryption.
                        Designed for everyone, especially families.
                    </p>

                    <ul class="feature-list">
                        <li class="feature-item">
                            <span class="feature-icon">📷</span>
                            <span>Automatic photo backup with deduplication</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">👥</span>
                            <span>Complete contact synchronization</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">💬</span>
                            <span>Secure message backup and threading</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">🔐</span>
                            <span>End-to-end AES-256 encryption</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">☁️</span>
                            <span>Supabase cloud storage integration</span>
                        </li>
                    </ul>

                    <button class="button" onclick="showScreen('auth')">Get Started</button>
                </div>
            </div>

            <!-- Authentication Screen -->
            <div id="auth" class="screen">
                <div style="text-align: center; padding: 40px 0 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Welcome Back</h1>
                    <p style="color: #666;">Sign in to access your secure backups</p>
                </div>

                <div class="card">
                    <div style="margin-bottom: 16px;">
                        <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="<EMAIL>">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="••••••••">
                    </div>
                    <button class="button" onclick="showScreen('onboarding')">🔐 Sign In</button>
                    <button class="button secondary">Create Account</button>
                </div>

                <div class="card" style="background: rgba(80, 227, 194, 0.1);">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Your Data is Secure</span>
                    </h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                        <li>• All data encrypted with AES-256 before upload</li>
                        <li>• Only you have the decryption keys</li>
                        <li>• We cannot access your personal information</li>
                        <li>• Your privacy is our top priority</li>
                    </ul>
                </div>
            </div>

            <!-- Onboarding Screen -->
            <div id="onboarding" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Let's Set You Up</h1>
                    <p style="color: #666;">We need a few permissions to keep your data safe</p>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>📊</span>
                        <span>Setup Progress</span>
                    </h3>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Progress</span>
                        <span style="color: #4A90E2; font-weight: bold;">67%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 67%;"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Permissions Needed</span>
                    </h3>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">📷</span>
                            <div>
                                <div style="font-weight: 600;">Photo Access</div>
                                <div style="font-size: 14px; color: #666;">Backup your precious family photos</div>
                            </div>
                        </div>
                        <span style="background: #50e3c2; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✓ Allowed</span>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">👥</span>
                            <div>
                                <div style="font-weight: 600;">Contact Access</div>
                                <div style="font-size: 14px; color: #666;">Save important phone numbers</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission(this, 'contacts')">Allow</button>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">💬</span>
                            <div>
                                <div style="font-weight: 600;">Message Access</div>
                                <div style="font-size: 14px; color: #666;">Backup important conversations</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission(this, 'messages')">Allow</button>
                    </div>
                </div>

                <button class="button" onclick="showScreen('subscription')">Continue to Subscription</button>
            </div>

            <!-- Subscription Screen -->
            <div id="subscription" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">💎</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Choose Your Plan</h1>
                    <p style="color: #666;">Mix & match services or get everything</p>
                </div>

                <div class="card" style="border: 2px solid #4A90E2;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                        <h3 style="display: flex; align-items: center; gap: 8px;">
                            <span>🏆</span>
                            <span>Complete Backup</span>
                        </h3>
                        <span style="background: #4A90E2; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">RECOMMENDED</span>
                    </div>
                    <div style="font-size: 32px; font-weight: bold; color: #4A90E2; margin-bottom: 8px;">$6.99<span style="font-size: 16px;">/month</span></div>
                    <ul style="margin-bottom: 16px;">
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Photos + Contacts + Messages</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>100GB Storage</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Priority Support</span>
                        </li>
                    </ul>
                    <button class="button" onclick="selectPlan('complete')">Select Complete Plan</button>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>📱</span>
                        <span>Individual Services</span>
                    </h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">📷</div>
                            <div style="font-size: 12px; color: #666;">Photos</div>
                            <div style="font-weight: bold; color: #4A90E2;">$4.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">👥</div>
                            <div style="font-size: 12px; color: #666;">Contacts</div>
                            <div style="font-weight: bold; color: #4A90E2;">$0.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">💬</div>
                            <div style="font-size: 12px; color: #666;">Messages</div>
                            <div style="font-weight: bold; color: #4A90E2;">$1.99</div>
                        </div>
                    </div>
                </div>

                <button class="button" onclick="showMainApp()">Start Free Trial</button>
            </div>

            <!-- Dashboard Screen -->
            <div id="dashboard" class="screen">
                <div class="card">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>📊</span>
                        <span>Backup Status</span>
                    </h3>
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div class="status-indicator"></div>
                        <span style="margin-left: 8px;">All data backed up</span>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        Last backup: Today at 2:30 PM
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">📷</div>
                        <div class="stat-number" id="photos-count">1,247</div>
                        <div style="font-size: 14px; color: #666;">Photos</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">👥</div>
                        <div class="stat-number" id="contacts-count">156</div>
                        <div style="font-size: 14px; color: #666;">Contacts</div>
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💬</div>
                        <div class="stat-number" id="messages-count">2,341</div>
                        <div style="font-size: 14px; color: #666;">Messages</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💾</div>
                        <div class="stat-number">2.8 GB</div>
                        <div style="font-size: 14px; color: #666;">Total Size</div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>☁️</span>
                        <span>Cloud Storage</span>
                    </h3>
                    <div style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 18px; color: #333; margin-bottom: 8px;">
                            2.8 GB of 100 GB used
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 2.8%;"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #666;">
                            <span>2.8% used</span>
                            <span>97.2 GB remaining</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup Screen -->
            <div id="backup" class="screen">
                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🚀</span>
                        <span>Backup Center</span>
                    </h3>
                    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                        <button class="button" onclick="startBackup()">Start Full Backup</button>
                        <button class="button secondary">View History</button>
                    </div>
                    <div style="font-size: 14px; color: #666; text-align: center;">
                        Next automatic backup: Tomorrow at 2:30 PM
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>📷</span>
                        <span>Photos</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">1,247 photos backed up</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div style="margin: 16px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;" id="photo-progress">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="photo-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="backupPhotos()">Backup Photos Now</button>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>👥</span>
                        <span>Contacts</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">156 contacts synced</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div style="margin: 16px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;" id="contact-progress">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="contact-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="backupContacts()">Sync Contacts Now</button>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>💬</span>
                        <span>Messages</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">2,341 messages backed up</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div style="margin: 16px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;" id="message-progress">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="message-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="backupMessages()">Backup Messages Now</button>
                </div>
            </div>

            <!-- Settings Screen -->
            <div id="settings" class="screen">
                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>👤</span>
                        <span>Account</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Email</div>
                            <div style="font-size: 14px; color: #666;"><EMAIL></div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Edit</button>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Subscription</div>
                            <div style="font-size: 14px; color: #666;">Complete Backup Plan</div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Manage</button>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔄</span>
                        <span>Backup Settings</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Auto Backup</div>
                            <div style="font-size: 14px; color: #666;">Automatically backup new data</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">WiFi Only</div>
                            <div style="font-size: 14px; color: #666;">Only backup on WiFi connection</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Security</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Encryption</div>
                            <div style="font-size: 14px; color: #666;">AES-256 encryption enabled</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Two-Factor Auth</div>
                            <div style="font-size: 14px; color: #666;">Extra security for your account</div>
                        </div>
                        <div class="switch" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Biometric Lock</div>
                            <div style="font-size: 14px; color: #666;">Use fingerprint to unlock app</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <button class="button secondary" style="margin-bottom: 8px;">Contact Support</button>
                    <button class="button secondary" style="margin-bottom: 8px;">Privacy Policy</button>
                    <button class="button secondary" style="color: #d0021b; border-color: #d0021b;">Sign Out</button>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tabs" id="tabs">
                <button class="tab active" onclick="showTab('dashboard')">
                    <div style="font-size: 20px; margin-bottom: 4px;">📊</div>
                    <div>Dashboard</div>
                </button>
                <button class="tab" onclick="showTab('backup')">
                    <div style="font-size: 20px; margin-bottom: 4px;">💾</div>
                    <div>Backup</div>
                </button>
                <button class="tab" onclick="showTab('settings')">
                    <div style="font-size: 20px; margin-bottom: 4px;">⚙️</div>
                    <div>Settings</div>
                </button>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="controls">
            <h2 style="text-align: center; margin-bottom: 20px; color: #333;">🎯 SafeKeep Complete Demo</h2>

            <div style="margin-bottom: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">📱 Navigate Screens</h3>
                <button class="control-btn active" onclick="showScreen('welcome')">Welcome</button>
                <button class="control-btn" onclick="showScreen('auth')">Sign In</button>
                <button class="control-btn" onclick="showScreen('onboarding')">Setup</button>
                <button class="control-btn" onclick="showScreen('subscription')">Plans</button>
                <button class="control-btn" onclick="showMainApp()">Main App</button>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">🚀 Demo Actions</h3>
                <button class="control-btn" onclick="simulateBackup()">Simulate Backup</button>
                <button class="control-btn" onclick="updateStats()">Update Stats</button>
                <button class="control-btn" onclick="showNotification('Demo notification!')">Show Alert</button>
                <button class="control-btn" onclick="resetDemo()">Reset Demo</button>
            </div>

            <div style="background: rgba(74, 144, 226, 0.1); padding: 15px; border-radius: 8px;">
                <h4 style="margin-bottom: 8px; color: #333;">💡 About This Demo</h4>
                <p style="font-size: 14px; color: #666; line-height: 1.6;">
                    Complete SafeKeep app experience with all screens: Welcome → Authentication → Onboarding →
                    Subscription → Main App (Dashboard, Backup, Settings). All features are interactive including
                    backup simulation, progress tracking, and settings management.
                </p>
            </div>

            <div style="margin-top: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">✅ Features Included</h3>
                <div style="font-size: 14px;">
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Complete user onboarding flow</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Interactive backup system</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Real-time progress tracking</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Modular pricing plans</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Security & encryption info</div>
                    <div style="padding: 5px 0;">✓ Settings & configuration</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentScreen = 'welcome';
        let isMainApp = false;

        function showScreen(screenId) {
            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // Show target screen
            document.getElementById(screenId).classList.add('active');
            currentScreen = screenId;

            // Update title
            const titles = {
                'welcome': 'SafeKeep',
                'auth': 'Sign In',
                'onboarding': 'Setup',
                'subscription': 'Choose Plan',
                'dashboard': 'Dashboard',
                'backup': 'Backup',
                'settings': 'Settings'
            };
            document.getElementById('title').textContent = titles[screenId] || 'SafeKeep';

            // Update control buttons
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));

            // Show/hide tabs for main app
            const tabs = document.getElementById('tabs');
            if (['dashboard', 'backup', 'settings'].includes(screenId)) {
                tabs.classList.add('show');
                isMainApp = true;
            } else {
                tabs.classList.remove('show');
                isMainApp = false;
            }
        }

        function showTab(tabName) {
            showScreen(tabName);

            // Update active tab
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.closest('.tab').classList.add('active');
        }

        function showMainApp() {
            showScreen('dashboard');
            isMainApp = true;
        }

        function goBack() {
            if (isMainApp) {
                showScreen('subscription');
            } else {
                const backFlow = {
                    'auth': 'welcome',
                    'onboarding': 'auth',
                    'subscription': 'onboarding'
                };
                if (backFlow[currentScreen]) {
                    showScreen(backFlow[currentScreen]);
                }
            }
        }

        function grantPermission(button, type) {
            button.textContent = '✓ Granted';
            button.style.background = '#50e3c2';
            button.style.borderColor = '#50e3c2';
            button.style.color = 'white';
            button.disabled = true;
            showNotification(`${type} permission granted!`);
        }

        function selectPlan(planType) {
            showNotification(`${planType} plan selected!`);
            setTimeout(() => showMainApp(), 1000);
        }

        function toggleSwitch(element) {
            element.classList.toggle('on');
            const isOn = element.classList.contains('on');
            showNotification(`Setting ${isOn ? 'enabled' : 'disabled'}`);
        }

        function startBackup() {
            showNotification('Starting full backup...');
            simulateBackup();
        }

        function backupPhotos() {
            showNotification('Starting photo backup...');
            simulateProgress('photo');
        }

        function backupContacts() {
            showNotification('Starting contact sync...');
            simulateProgress('contact');
        }

        function backupMessages() {
            showNotification('Starting message backup...');
            simulateProgress('message');
        }

        function simulateBackup() {
            showScreen('backup');
            ['photo', 'contact', 'message'].forEach((type, index) => {
                setTimeout(() => simulateProgress(type), index * 1500);
            });
        }

        function simulateProgress(type) {
            const progressBar = document.getElementById(type + '-bar');
            const progressText = document.getElementById(type + '-progress');

            let progress = 0;
            progressBar.style.width = '0%';

            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    showNotification(`${type} backup completed!`);
                }
            }, 200);
        }

        function updateStats() {
            const photosCount = document.getElementById('photos-count');
            const contactsCount = document.getElementById('contacts-count');
            const messagesCount = document.getElementById('messages-count');

            if (photosCount) photosCount.textContent = (parseInt(photosCount.textContent.replace(',', '')) + Math.floor(Math.random() * 50)).toLocaleString();
            if (contactsCount) contactsCount.textContent = parseInt(contactsCount.textContent) + Math.floor(Math.random() * 5);
            if (messagesCount) messagesCount.textContent = (parseInt(messagesCount.textContent.replace(',', '')) + Math.floor(Math.random() * 100)).toLocaleString();

            showNotification('Statistics updated!');
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4A90E2;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                font-weight: 600;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => notification.style.transform = 'translateX(0)', 100);
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        function resetDemo() {
            showScreen('welcome');
            document.querySelectorAll('.progress-fill').forEach(bar => bar.style.width = '100%');
            document.querySelectorAll('[id$="-progress"]').forEach(text => text.textContent = '100%');
            showNotification('Demo reset!');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showScreen('welcome');
            setTimeout(() => showNotification('👋 Welcome! Use the controls to explore the app'), 2000);
        });
    </script>
</body>
</html>
