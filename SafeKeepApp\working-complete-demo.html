<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Complete App Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 375px 1fr;
            gap: 30px;
            align-items: start;
        }

        .phone {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            height: 812px;
            position: relative;
        }

        .header {
            background: #4A90E2;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .screen {
            height: calc(100% - 120px);
            overflow-y: auto;
            padding: 20px;
            display: none;
        }

        .screen.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 8px;
            transition: background 0.3s;
        }

        .button:hover {
            background: #357abd;
        }

        .button.secondary {
            background: none;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }

        .tabs {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: none;
            height: 60px;
        }

        .tabs.show {
            display: flex;
        }

        .tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #666;
            font-size: 12px;
            border: none;
            background: none;
            transition: color 0.3s;
        }

        .tab.active {
            color: #4A90E2;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4A90E2;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .controls {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .control-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            margin: 5px;
            cursor: pointer;
        }

        .control-btn:hover {
            background: #5a6268;
        }

        .control-btn.active {
            background: #4A90E2;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .switch {
            width: 50px;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.on {
            background: #4A90E2;
        }

        .switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }

        .switch.on::after {
            transform: translateX(20px);
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #50e3c2;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .phone {
                width: 100%;
                max-width: 375px;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Phone Demo -->
        <div class="phone">
            <div class="header">
                <button onclick="goBack()" style="background: none; border: none; color: white; font-size: 16px; cursor: pointer;">←</button>
                <h1 id="title">SafeKeep</h1>
                <div></div>
            </div>

            <!-- Welcome Screen -->
            <div id="welcome" class="screen active">
                <div style="text-align: center; padding: 40px 0;">
                    <div style="font-size: 80px; margin-bottom: 20px;">🛡️</div>
                    <h1 style="font-size: 32px; font-weight: bold; color: #333; margin-bottom: 8px;">SafeKeep</h1>
                    <p style="font-size: 18px; color: #666; margin-bottom: 32px;">Your Precious Data, Perfectly Protected</p>
                </div>

                <div class="card">
                    <p style="text-align: center; margin-bottom: 24px; color: #666; line-height: 1.5;">
                        Securely backup your photos, contacts, and messages with military-grade encryption.
                        Designed for everyone, especially families.
                    </p>

                    <ul class="feature-list">
                        <li class="feature-item">
                            <span class="feature-icon">📷</span>
                            <span>Automatic photo backup with deduplication</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">👥</span>
                            <span>Complete contact synchronization</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">💬</span>
                            <span>Secure message backup and threading</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">🔐</span>
                            <span>End-to-end AES-256 encryption</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">☁️</span>
                            <span>Supabase cloud storage integration</span>
                        </li>
                    </ul>

                    <button class="button" onclick="showScreen('auth')">Get Started</button>
                </div>
            </div>

            <!-- Authentication Screen -->
            <div id="auth" class="screen">
                <div style="text-align: center; padding: 40px 0 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Welcome Back</h1>
                    <p style="color: #666;">Sign in to access your secure backups</p>
                </div>

                <div class="card">
                    <div style="margin-bottom: 16px;">
                        <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="<EMAIL>">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" value="••••••••">
                    </div>
                    <button class="button" onclick="showMainApp()">🔐 Sign In</button>
                    <button class="button secondary" onclick="showScreen('register')">Create Account</button>
                </div>

                <div class="card" style="background: rgba(80, 227, 194, 0.1);">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Your Data is Secure</span>
                    </h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                        <li>• All data encrypted with AES-256 before upload</li>
                        <li>• Only you have the decryption keys</li>
                        <li>• We cannot access your personal information</li>
                        <li>• Your privacy is our top priority</li>
                    </ul>
                </div>
            </div>

            <!-- Registration Screen -->
            <div id="register" class="screen">
                <div style="text-align: center; padding: 40px 0 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Create Account</h1>
                    <p style="color: #666;">Join SafeKeep to protect your precious data</p>
                </div>

                <div class="card">
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Full Name</label>
                        <input type="text" placeholder="Enter your full name" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Email Address</label>
                        <input type="email" placeholder="Enter your email address" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Password</label>
                        <input type="password" placeholder="Create a strong password" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Confirm Password</label>
                        <input type="password" placeholder="Confirm your password" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                    </div>

                    <div style="margin-bottom: 16px; padding: 12px; background: rgba(74, 144, 226, 0.1); border-radius: 6px;">
                        <label style="display: flex; align-items: flex-start; cursor: pointer;">
                            <input type="checkbox" style="margin-right: 8px; margin-top: 2px;">
                            <span style="font-size: 14px; line-height: 1.4;">
                                I agree to the <a href="#" style="color: #4A90E2; text-decoration: none;">Terms of Service</a> and
                                <a href="#" style="color: #4A90E2; text-decoration: none;">Privacy Policy</a>
                            </span>
                        </label>
                    </div>

                    <button class="button" onclick="createAccount()">🛡️ Create Account</button>
                    <button class="button secondary" onclick="showScreen('auth')">Already have an account? Sign In</button>
                </div>

                <div class="card" style="background: rgba(80, 227, 194, 0.1);">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Your Account Will Be Secure</span>
                    </h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                        <li>• Password encrypted with industry-standard security</li>
                        <li>• Account protected with optional two-factor authentication</li>
                        <li>• Your personal data is never shared or sold</li>
                        <li>• Complete control over your backup preferences</li>
                    </ul>
                </div>
            </div>

            <!-- Payment Screen -->
            <div id="payment" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">💳</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Secure Payment</h1>
                    <p style="color: #666;">Complete your subscription with Stripe</p>
                </div>

                <div class="card" style="border: 2px solid #4A90E2;">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>📋</span>
                        <span>Order Summary</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;" id="selected-plan">Complete Backup Plan</div>
                            <div style="font-size: 14px; color: #666;">Photos + Contacts + Messages + 100GB Storage</div>
                        </div>
                        <div style="font-weight: bold; color: #4A90E2; font-size: 18px;" id="plan-price">$6.99/month</div>
                    </div>
                    <div style="border-top: 1px solid #e0e0e0; margin: 12px 0; padding-top: 12px;">
                        <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 16px;">
                            <span>Total</span>
                            <span style="color: #4A90E2;" id="total-price">$6.99/month</span>
                        </div>
                        <div style="font-size: 12px; color: #666; margin-top: 4px;">7-day free trial • Cancel anytime</div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>💳</span>
                        <span>Payment Information</span>
                    </h3>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Card Number</label>
                        <input type="text" placeholder="1234 5678 9012 3456" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" maxlength="19">
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                        <div>
                            <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Expiry Date</label>
                            <input type="text" placeholder="MM/YY" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" maxlength="5">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">CVC</label>
                            <input type="text" placeholder="123" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;" maxlength="4">
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Cardholder Name</label>
                        <input type="text" placeholder="John Doe" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 600; color: #333;">Billing Address</label>
                        <input type="text" placeholder="123 Main Street" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px; margin-bottom: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <input type="text" placeholder="City" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                            <input type="text" placeholder="ZIP Code" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; font-size: 16px;">
                        </div>
                    </div>

                    <button class="button" onclick="processPayment()">🔒 Complete Payment</button>
                </div>

                <div class="card" style="background: rgba(80, 227, 194, 0.1);">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>🔒</span>
                        <span>Secure Payment by Stripe</span>
                    </h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                        <li>• Your payment information is encrypted and secure</li>
                        <li>• Processed by Stripe, trusted by millions</li>
                        <li>• 7-day free trial - no charge until trial ends</li>
                        <li>• Cancel anytime with one click</li>
                    </ul>
                </div>
            </div>

            <!-- Onboarding Screen -->
            <div id="onboarding" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">🛡️</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Let's Set You Up</h1>
                    <p style="color: #666;">We need a few permissions to keep your data safe</p>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>📊</span>
                        <span>Setup Progress</span>
                    </h3>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Progress</span>
                        <span style="color: #4A90E2; font-weight: bold;" id="setup-progress">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="setup-progress-bar" style="width: 0%;"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Permissions Needed</span>
                    </h3>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">📷</span>
                            <div>
                                <div style="font-weight: 600;">Photo Access</div>
                                <div style="font-size: 14px; color: #666;">Backup your precious family photos</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission(this, 'photos')">Allow</button>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">👥</span>
                            <div>
                                <div style="font-weight: 600;">Contact Access</div>
                                <div style="font-size: 14px; color: #666;">Save important phone numbers</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission(this, 'contacts')">Allow</button>
                    </div>

                    <div class="backup-item">
                        <div style="display: flex; align-items: center;">
                            <span class="feature-icon">💬</span>
                            <div>
                                <div style="font-weight: 600;">Message Access</div>
                                <div style="font-size: 14px; color: #666;">Backup important conversations</div>
                            </div>
                        </div>
                        <button class="button" style="width: auto; padding: 4px 12px; font-size: 12px;" onclick="grantPermission(this, 'messages')">Allow</button>
                    </div>
                </div>

                <button class="button" id="continue-button" onclick="startFirstBackup()" disabled style="background: #ccc; cursor: not-allowed;">Complete Setup (0/3 permissions)</button>
            </div>

            <!-- Subscription Screen -->
            <div id="subscription" class="screen">
                <div style="text-align: center; padding: 20px 0;">
                    <div style="font-size: 64px; margin-bottom: 16px;">💎</div>
                    <h1 style="font-size: 24px; color: #333; margin-bottom: 8px;">Choose Your Plan</h1>
                    <p style="color: #666;">Mix & match services or get everything</p>
                </div>

                <div class="card" style="border: 2px solid #4A90E2;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                        <h3 style="display: flex; align-items: center; gap: 8px;">
                            <span>🏆</span>
                            <span>Complete Backup</span>
                        </h3>
                        <span style="background: #4A90E2; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">RECOMMENDED</span>
                    </div>
                    <div style="font-size: 32px; font-weight: bold; color: #4A90E2; margin-bottom: 8px;">$6.99<span style="font-size: 16px;">/month</span></div>
                    <ul style="margin-bottom: 16px;">
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Photos + Contacts + Messages</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>100GB Storage</span>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>Priority Support</span>
                        </li>
                    </ul>
                    <button class="button" onclick="selectPlan('complete')">Select Complete Plan</button>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>📱</span>
                        <span>Individual Services</span>
                    </h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">📷</div>
                            <div style="font-size: 12px; color: #666;">Photos</div>
                            <div style="font-weight: bold; color: #4A90E2;">$4.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">👥</div>
                            <div style="font-size: 12px; color: #666;">Contacts</div>
                            <div style="font-weight: bold; color: #4A90E2;">$0.99</div>
                        </div>
                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 20px; margin-bottom: 4px;">💬</div>
                            <div style="font-size: 12px; color: #666;">Messages</div>
                            <div style="font-weight: bold; color: #4A90E2;">$1.99</div>
                        </div>
                    </div>
                </div>

                <button class="button" onclick="startFreeTrial()">Start Free Trial</button>
            </div>

            <!-- Dashboard Screen -->
            <div id="dashboard" class="screen">
                <div class="card">
                    <h3 style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span>📊</span>
                        <span>Backup Status</span>
                    </h3>
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div class="status-indicator"></div>
                        <span style="margin-left: 8px;">All data backed up</span>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        Last backup: Today at 2:30 PM
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">📷</div>
                        <div class="stat-number" id="photos-count">1,247</div>
                        <div style="font-size: 14px; color: #666;">Photos</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">👥</div>
                        <div class="stat-number" id="contacts-count">156</div>
                        <div style="font-size: 14px; color: #666;">Contacts</div>
                    </div>
                </div>

                <div class="stat-grid">
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💬</div>
                        <div class="stat-number" id="messages-count">2,341</div>
                        <div style="font-size: 14px; color: #666;">Messages</div>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 32px; margin-bottom: 8px;">💾</div>
                        <div class="stat-number">2.8 GB</div>
                        <div style="font-size: 14px; color: #666;">Total Size</div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>☁️</span>
                        <span>Cloud Storage</span>
                    </h3>
                    <div style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 18px; color: #333; margin-bottom: 8px;">
                            2.8 GB of 100 GB used
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 2.8%;"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #666;">
                            <span>2.8% used</span>
                            <span>97.2 GB remaining</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup Screen -->
            <div id="backup" class="screen">
                <!-- Empty State for New Users -->
                <div id="backup-empty-state" class="card" style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 64px; margin-bottom: 16px;">📦</div>
                    <h3 style="margin-bottom: 12px; color: #333;">No Backups Yet</h3>
                    <p style="color: #666; margin-bottom: 24px; line-height: 1.5;">
                        Your data is waiting to be protected. Start your first backup to keep your precious memories safe in the cloud.
                    </p>
                    <button class="button" onclick="startFirstBackupProcess()" style="margin-bottom: 16px;">
                        🚀 Start Your First Backup
                    </button>
                    <div style="font-size: 14px; color: #666;">
                        <div>✓ Photos, contacts, and messages will be encrypted</div>
                        <div>✓ Automatic daily backups after first setup</div>
                        <div>✓ Access your data from any device</div>
                    </div>
                </div>

                <!-- Active Backup State (hidden initially for new users) -->
                <div id="backup-active-state" style="display: none;">
                    <div class="card">
                        <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                            <span>🚀</span>
                            <span>Backup Center</span>
                        </h3>
                        <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                            <button class="button" onclick="startBackup()">Start Full Backup</button>
                            <button class="button secondary">View History</button>
                        </div>
                        <div style="font-size: 14px; color: #666; text-align: center;">
                            Next automatic backup: Tomorrow at 2:30 PM
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                            <span>📷</span>
                            <span>Photos</span>
                        </h3>
                        <div class="backup-item">
                            <div>
                                <div style="font-weight: 600;">Status</div>
                                <div style="font-size: 14px; color: #666;" id="photos-status-text">1,247 photos backed up</div>
                            </div>
                            <div class="status-indicator"></div>
                        </div>
                        <div style="margin: 16px 0;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                <span style="font-size: 14px;">Progress</span>
                                <span style="font-size: 14px; color: #4A90E2;" id="photo-progress">100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="photo-bar" style="width: 100%;"></div>
                            </div>
                        </div>
                        <button class="button secondary" onclick="backupPhotos()">Backup Photos Now</button>
                    </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>👥</span>
                        <span>Contacts</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">156 contacts synced</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div style="margin: 16px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;" id="contact-progress">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="contact-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="backupContacts()">Sync Contacts Now</button>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>💬</span>
                        <span>Messages</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Status</div>
                            <div style="font-size: 14px; color: #666;">2,341 messages backed up</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div style="margin: 16px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 14px;">Progress</span>
                            <span style="font-size: 14px; color: #4A90E2;" id="message-progress">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="message-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <button class="button secondary" onclick="backupMessages()">Backup Messages Now</button>
                </div>
            </div>

            <!-- Settings Screen -->
            <div id="settings" class="screen">
                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>👤</span>
                        <span>Account</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Email</div>
                            <div style="font-size: 14px; color: #666;"><EMAIL></div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Edit</button>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Subscription</div>
                            <div style="font-size: 14px; color: #666;">Complete Backup Plan</div>
                        </div>
                        <button class="button secondary" style="width: auto; padding: 8px 16px; font-size: 14px;">Manage</button>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔄</span>
                        <span>Backup Settings</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Auto Backup</div>
                            <div style="font-size: 14px; color: #666;">Automatically backup new data</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">WiFi Only</div>
                            <div style="font-size: 14px; color: #666;">Only backup on WiFi connection</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                        <span>🔐</span>
                        <span>Security</span>
                    </h3>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Encryption</div>
                            <div style="font-size: 14px; color: #666;">AES-256 encryption enabled</div>
                        </div>
                        <div class="status-indicator"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Two-Factor Auth</div>
                            <div style="font-size: 14px; color: #666;">Extra security for your account</div>
                        </div>
                        <div class="switch" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="backup-item">
                        <div>
                            <div style="font-weight: 600;">Biometric Lock</div>
                            <div style="font-size: 14px; color: #666;">Use fingerprint to unlock app</div>
                        </div>
                        <div class="switch on" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>

                <div class="card">
                    <button class="button secondary" style="margin-bottom: 8px;">Contact Support</button>
                    <button class="button secondary" style="margin-bottom: 8px;">Privacy Policy</button>
                    <button class="button secondary" style="color: #d0021b; border-color: #d0021b;">Sign Out</button>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tabs" id="tabs">
                <button class="tab active" onclick="showTab('dashboard')">
                    <div style="font-size: 20px; margin-bottom: 4px;">📊</div>
                    <div>Dashboard</div>
                </button>
                <button class="tab" onclick="showTab('backup')">
                    <div style="font-size: 20px; margin-bottom: 4px;">💾</div>
                    <div>Backup</div>
                </button>
                <button class="tab" onclick="showTab('settings')">
                    <div style="font-size: 20px; margin-bottom: 4px;">⚙️</div>
                    <div>Settings</div>
                </button>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="controls">
            <h2 style="text-align: center; margin-bottom: 20px; color: #333;">🎯 SafeKeep Complete Demo</h2>

            <div style="margin-bottom: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">📱 Navigate Screens</h3>
                <button class="control-btn active" onclick="showScreen('welcome')">Welcome</button>
                <button class="control-btn" onclick="showScreen('auth')">Sign In</button>
                <button class="control-btn" onclick="showScreen('register')">Register</button>
                <button class="control-btn" onclick="showScreen('subscription')">Plans</button>
                <button class="control-btn" onclick="showScreen('payment')">Payment</button>
                <button class="control-btn" onclick="showScreen('onboarding')">Setup</button>
                <button class="control-btn" onclick="showMainApp()">Main App</button>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">🚀 Demo Actions</h3>
                <button class="control-btn" onclick="simulateBackup()">Simulate Backup</button>
                <button class="control-btn" onclick="updateStats()">Update Stats</button>
                <button class="control-btn" onclick="showNotification('Demo notification!')">Show Alert</button>
                <button class="control-btn" onclick="resetDemo()">Reset Demo</button>
            </div>

            <div style="background: rgba(74, 144, 226, 0.1); padding: 15px; border-radius: 8px;">
                <h4 style="margin-bottom: 8px; color: #333;">💡 About This Demo</h4>
                <p style="font-size: 14px; color: #666; line-height: 1.6;">
                    Complete SafeKeep app experience with all screens: Welcome → Authentication → Onboarding →
                    Subscription → Main App (Dashboard, Backup, Settings). All features are interactive including
                    backup simulation, progress tracking, and settings management.
                </p>
            </div>

            <div style="margin-top: 20px;">
                <h3 style="margin-bottom: 10px; color: #333;">✅ Features Included</h3>
                <div style="font-size: 14px;">
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Complete user onboarding flow</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Interactive backup system</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Real-time progress tracking</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Modular pricing plans</div>
                    <div style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">✓ Security & encryption info</div>
                    <div style="padding: 5px 0;">✓ Settings & configuration</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentScreen = 'welcome';
        let isMainApp = false;

        function showScreen(screenId) {
            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // Show target screen
            document.getElementById(screenId).classList.add('active');
            currentScreen = screenId;

            // Update title
            const titles = {
                'welcome': 'SafeKeep',
                'auth': 'Sign In',
                'register': 'Create Account',
                'subscription': 'Choose Plan',
                'payment': 'Payment',
                'onboarding': 'Setup',
                'dashboard': 'Dashboard',
                'backup': 'Backup',
                'settings': 'Settings'
            };
            document.getElementById('title').textContent = titles[screenId] || 'SafeKeep';

            // Update control buttons
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));

            // Show/hide tabs for main app
            const tabs = document.getElementById('tabs');
            if (['dashboard', 'backup', 'settings'].includes(screenId)) {
                tabs.classList.add('show');
                isMainApp = true;
            } else {
                tabs.classList.remove('show');
                isMainApp = false;
            }
        }

        function showTab(tabName) {
            showScreen(tabName);

            // Update active tab
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.closest('.tab').classList.add('active');
        }

        function showMainApp() {
            showScreen('dashboard');
            isMainApp = true;
        }

        function goBack() {
            if (isMainApp) {
                showScreen('auth');
            } else {
                const backFlow = {
                    'auth': 'welcome',
                    'register': 'welcome',
                    'subscription': 'register',
                    'payment': 'subscription',
                    'onboarding': 'payment'
                };
                if (backFlow[currentScreen]) {
                    showScreen(backFlow[currentScreen]);
                }
            }
        }

        function createAccount() {
            // Validate form (simplified for demo)
            const inputs = document.querySelectorAll('#register input');
            let allFilled = true;

            inputs.forEach(input => {
                if (input.type !== 'checkbox' && !input.value.trim()) {
                    allFilled = false;
                    input.style.borderColor = '#d0021b';
                } else {
                    input.style.borderColor = '#e0e0e0';
                }
            });

            const checkbox = document.querySelector('#register input[type="checkbox"]');
            if (!checkbox.checked) {
                allFilled = false;
                showNotification('Please agree to the Terms of Service and Privacy Policy');
                return;
            }

            if (!allFilled) {
                showNotification('Please fill in all required fields');
                return;
            }

            // Simulate account creation
            showNotification('Account created successfully! Choose your plan...');
            setTimeout(() => {
                showScreen('subscription');
            }, 1500);
        }

        let grantedPermissions = 0;
        const totalPermissions = 3;

        function grantPermission(button, type) {
            button.textContent = '✓ Granted';
            button.style.background = '#50e3c2';
            button.style.borderColor = '#50e3c2';
            button.style.color = 'white';
            button.disabled = true;

            grantedPermissions++;
            updateSetupProgress();
            showNotification(`${type} permission granted!`);
        }

        function updateSetupProgress() {
            const progressPercent = Math.round((grantedPermissions / totalPermissions) * 100);
            const progressBar = document.getElementById('setup-progress-bar');
            const progressText = document.getElementById('setup-progress');
            const continueButton = document.getElementById('continue-button');

            if (progressBar) progressBar.style.width = progressPercent + '%';
            if (progressText) progressText.textContent = progressPercent + '%';

            if (continueButton) {
                continueButton.textContent = `Complete Setup (${grantedPermissions}/${totalPermissions} permissions)`;

                if (grantedPermissions === totalPermissions) {
                    continueButton.disabled = false;
                    continueButton.style.background = '#4A90E2';
                    continueButton.style.cursor = 'pointer';
                    continueButton.textContent = '✅ Start First Backup';
                    showNotification('All permissions granted! Ready to start your first backup.');
                }
            }
        }

        function resetOnboarding() {
            grantedPermissions = 0;
            updateSetupProgress();

            // Reset all permission buttons
            document.querySelectorAll('#onboarding .button').forEach(button => {
                if (button.id !== 'continue-button') {
                    button.textContent = 'Allow';
                    button.style.background = '#4A90E2';
                    button.style.borderColor = '#4A90E2';
                    button.style.color = 'white';
                    button.disabled = false;
                }
            });
        }

        function startFirstBackup() {
            showNotification('Taking you to your first backup...');
            setTimeout(() => {
                showMainApp();
                showTab('backup');
            }, 1000);
        }

        function startFirstBackupProcess() {
            // Hide empty state and show active backup state
            document.getElementById('backup-empty-state').style.display = 'none';
            document.getElementById('backup-active-state').style.display = 'block';

            // Reset all backup progress for first-time experience
            document.getElementById('photos-status-text').textContent = '0 photos backed up';
            document.getElementById('photo-progress').textContent = '0%';
            document.getElementById('photo-bar').style.width = '0%';

            // Start the backup simulation
            showNotification('Starting your first backup...');
            setTimeout(() => {
                simulateFirstBackup();
            }, 1000);
        }

        function simulateFirstBackup() {
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                // Update photos progress
                document.getElementById('photo-progress').textContent = Math.round(progress) + '%';
                document.getElementById('photo-bar').style.width = progress + '%';

                // Update status text based on progress
                const photosCount = Math.round((progress / 100) * 1247);
                document.getElementById('photos-status-text').textContent = `${photosCount} photos backed up`;

                if (progress >= 100) {
                    clearInterval(interval);
                    showNotification('First backup complete! Your data is now safe.');

                    // Update status indicator to show completion
                    const statusIndicator = document.querySelector('#backup-active-state .status-indicator');
                    if (statusIndicator) {
                        statusIndicator.style.background = '#50e3c2';
                    }
                }
            }, 500);
        }

        function selectPlan(planType) {
            // Update payment screen with selected plan
            document.getElementById('selected-plan').textContent = 'Complete Backup Plan';
            document.getElementById('plan-price').textContent = '$6.99/month';
            document.getElementById('total-price').textContent = '$6.99/month';

            showNotification(`${planType} plan selected! Proceeding to payment...`);
            setTimeout(() => showScreen('payment'), 1000);
        }

        function startFreeTrial() {
            // Update payment screen for free trial
            document.getElementById('selected-plan').textContent = 'Free Trial';
            document.getElementById('plan-price').textContent = '$0.00';
            document.getElementById('total-price').textContent = '$0.00';

            showNotification('Starting free trial! Proceeding to payment setup...');
            setTimeout(() => showScreen('payment'), 1000);
        }

        function processPayment() {
            // Validate payment form (simplified for demo)
            const paymentInputs = document.querySelectorAll('#payment input');
            let allFilled = true;

            paymentInputs.forEach(input => {
                if (!input.value.trim()) {
                    allFilled = false;
                    input.style.borderColor = '#d0021b';
                } else {
                    input.style.borderColor = '#e0e0e0';
                }
            });

            if (!allFilled) {
                showNotification('Please fill in all payment fields');
                return;
            }

            // Simulate payment processing
            showNotification('Processing payment securely...');

            // Show processing state
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '⏳ Processing...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                showNotification('Payment successful! Setting up your account...');
                setTimeout(() => showScreen('onboarding'), 1000);
            }, 2500);
        }

        function toggleSwitch(element) {
            element.classList.toggle('on');
            const isOn = element.classList.contains('on');
            showNotification(`Setting ${isOn ? 'enabled' : 'disabled'}`);
        }

        function startBackup() {
            showNotification('Starting full backup...');
            simulateBackup();
        }

        function backupPhotos() {
            showNotification('Starting photo backup...');
            simulateProgress('photo');
        }

        function backupContacts() {
            showNotification('Starting contact sync...');
            simulateProgress('contact');
        }

        function backupMessages() {
            showNotification('Starting message backup...');
            simulateProgress('message');
        }

        function simulateBackup() {
            showScreen('backup');
            ['photo', 'contact', 'message'].forEach((type, index) => {
                setTimeout(() => simulateProgress(type), index * 1500);
            });
        }

        function simulateProgress(type) {
            const progressBar = document.getElementById(type + '-bar');
            const progressText = document.getElementById(type + '-progress');

            let progress = 0;
            progressBar.style.width = '0%';

            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    showNotification(`${type} backup completed!`);
                }
            }, 200);
        }

        function updateStats() {
            const photosCount = document.getElementById('photos-count');
            const contactsCount = document.getElementById('contacts-count');
            const messagesCount = document.getElementById('messages-count');

            if (photosCount) photosCount.textContent = (parseInt(photosCount.textContent.replace(',', '')) + Math.floor(Math.random() * 50)).toLocaleString();
            if (contactsCount) contactsCount.textContent = parseInt(contactsCount.textContent) + Math.floor(Math.random() * 5);
            if (messagesCount) messagesCount.textContent = (parseInt(messagesCount.textContent.replace(',', '')) + Math.floor(Math.random() * 100)).toLocaleString();

            showNotification('Statistics updated!');
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4A90E2;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                font-weight: 600;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => notification.style.transform = 'translateX(0)', 100);
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        function resetDemo() {
            showScreen('welcome');
            document.querySelectorAll('.progress-fill').forEach(bar => bar.style.width = '100%');
            document.querySelectorAll('[id$="-progress"]').forEach(text => text.textContent = '100%');

            // Reset onboarding progress
            resetOnboarding();

            // Reset backup state to empty for new users
            document.getElementById('backup-empty-state').style.display = 'block';
            document.getElementById('backup-active-state').style.display = 'none';

            showNotification('Demo reset!');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showScreen('welcome');
            setTimeout(() => showNotification('👋 Welcome! Use the controls to explore the app'), 2000);
        });
    </script>
</body>
</html>
