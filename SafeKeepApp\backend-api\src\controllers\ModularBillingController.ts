import { Request, Response } from 'express';
import Stripe from 'stripe';
import ModularBillingIntegration from '../services/ModularBillingIntegration';
import { ApiResponse } from '../types/modular-pricing';

export class ModularBillingController {
  private billingIntegration: ModularBillingIntegration;
  private stripe: Stripe;

  constructor() {
    this.billingIntegration = new ModularBillingIntegration();
    
    const secretKey = process.env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    
    this.stripe = new Stripe(secretKey, {
      apiVersion: '2023-10-16',
    });
  }

  /**
   * Create payment intent for modular subscription
   */
  async createPaymentIntent(req: Request, res: Response): Promise<void> {
    try {
      const {
        amount,
        currency,
        serviceIds,
        userId,
        planId,
        planName,
        customerId,
        description,
        metadata
      } = req.body;

      // Validate required fields
      if (!amount || !currency || !serviceIds || !userId) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Missing required fields: amount, currency, serviceIds, userId',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const result = await this.billingIntegration.createPaymentIntent({
        amount,
        currency,
        serviceIds,
        userId,
        planId,
        planName,
        customerId,
        description,
        metadata
      });

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: result.data,
          timestamp: new Date().toISOString()
        };
        res.json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'PAYMENT_INTENT_ERROR',
            message: result.error || 'Failed to create payment intent',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('ModularBillingController.createPaymentIntent error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * Create modular subscription
   */
  async createSubscription(req: Request, res: Response): Promise<void> {
    try {
      const {
        userId,
        serviceIds,
        planId,
        priceCents,
        customerId,
        paymentMethodId,
        billingEmail
      } = req.body;

      // Validate required fields
      if (!userId || !serviceIds || !planId || !priceCents) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Missing required fields: userId, serviceIds, planId, priceCents',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const result = await this.billingIntegration.createSubscription({
        userId,
        serviceIds,
        planId,
        priceCents,
        customerId,
        paymentMethodId,
        billingEmail
      });

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: result.data,
          timestamp: new Date().toISOString()
        };
        res.json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'SUBSCRIPTION_ERROR',
            message: result.error || 'Failed to create subscription',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('ModularBillingController.createSubscription error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * Update existing subscription
   */
  async updateSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;
      const {
        serviceIds,
        planId,
        priceCents,
        prorationBehavior
      } = req.body;

      // Validate required fields
      if (!subscriptionId || !serviceIds || !planId || !priceCents) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Missing required fields: subscriptionId, serviceIds, planId, priceCents',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const result = await this.billingIntegration.updateSubscription({
        subscriptionId,
        newServiceIds: serviceIds,
        newPlanId: planId,
        newPriceCents: priceCents,
        prorationBehavior
      });

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: result.data,
          timestamp: new Date().toISOString()
        };
        res.json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'SUBSCRIPTION_UPDATE_ERROR',
            message: result.error || 'Failed to update subscription',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('ModularBillingController.updateSubscription error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;

      if (!subscriptionId) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Subscription ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const result = await this.billingIntegration.cancelSubscription(subscriptionId);

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: result.data,
          timestamp: new Date().toISOString()
        };
        res.json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'SUBSCRIPTION_CANCEL_ERROR',
            message: result.error || 'Failed to cancel subscription',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('ModularBillingController.cancelSubscription error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * Handle Stripe webhooks for modular subscriptions
   */
  async handleWebhook(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.headers['stripe-signature'] as string;
      const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

      if (!signature) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_SIGNATURE',
            message: 'Stripe signature is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!endpointSecret) {
        console.error('STRIPE_WEBHOOK_SECRET environment variable is not set');
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'CONFIGURATION_ERROR',
            message: 'Webhook endpoint secret not configured',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
        return;
      }

      let event: Stripe.Event;

      try {
        // Verify webhook signature
        event = this.stripe.webhooks.constructEvent(
          req.body,
          signature,
          endpointSecret
        );
      } catch (err) {
        console.error('Webhook signature verification failed:', err);
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_SIGNATURE',
            message: 'Webhook signature verification failed',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Process the webhook event
      const result = await this.billingIntegration.handleWebhook(event);

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: { received: true, event_type: event.type },
          timestamp: new Date().toISOString()
        };
        res.json(response);
      } else {
        console.error('Webhook processing failed:', result.error);
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'WEBHOOK_PROCESSING_ERROR',
            message: result.error || 'Failed to process webhook',
            timestamp: new Date().toISOString()
          }
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('ModularBillingController.handleWebhook error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * Get subscription details
   */
  async getSubscriptionDetails(req: Request, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;

      if (!subscriptionId) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Subscription ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['customer', 'items.data.price.product']
      });

      const response: ApiResponse = {
        success: true,
        data: {
          id: subscription.id,
          status: subscription.status,
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
          cancel_at_period_end: subscription.cancel_at_period_end,
          metadata: subscription.metadata,
          customer: subscription.customer,
          items: subscription.items.data
        },
        timestamp: new Date().toISOString()
      };
      res.json(response);
    } catch (error) {
      console.error('ModularBillingController.getSubscriptionDetails error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }
}

export default ModularBillingController;
