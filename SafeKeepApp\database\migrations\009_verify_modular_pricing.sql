-- SafeKeep Modular Pricing Verification
-- Migration 009: Verify the modular pricing migration was successful
-- Run this script after 008_modular_pricing_rls_policies.sql

-- ============================================================================
-- STEP 1: Verify table structure and data
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '=== MODULAR PRICING MIGRATION VERIFICATION ===';
    RAISE NOTICE '';
END $$;

-- Check service types
DO $$
DECLARE
    service_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO service_count FROM public.service_types WHERE is_active = TRUE;
    RAISE NOTICE '1. Service Types: % active services found', service_count;
    
    IF service_count < 3 THEN
        RAISE WARNING 'Expected at least 3 service types (contacts, messages, photos)';
    END IF;
END $$;

-- Check subscription plans
DO $$
DECLARE
    plan_count INTEGER;
    combination_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO plan_count FROM public.subscription_plans WHERE is_active = TRUE;
    SELECT COUNT(*) INTO combination_count FROM public.subscription_plans WHERE is_active = TRUE AND is_combination = TRUE;
    
    RAISE NOTICE '2. Subscription Plans: % total plans, % combination plans', plan_count, combination_count;
    
    IF plan_count < 7 THEN
        RAISE WARNING 'Expected at least 7 subscription plans';
    END IF;
END $$;

-- Check plan-service relationships
DO $$
DECLARE
    relationship_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO relationship_count FROM public.plan_services WHERE is_included = TRUE;
    RAISE NOTICE '3. Plan-Service Relationships: % relationships defined', relationship_count;
    
    IF relationship_count < 13 THEN
        RAISE WARNING 'Expected at least 13 plan-service relationships';
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Test pricing calculation functions
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING PRICING CALCULATION FUNCTIONS ===';
END $$;

-- Test individual service pricing
DO $$
DECLARE
    pricing_result RECORD;
BEGIN
    SELECT * INTO pricing_result FROM public.calculate_optimal_pricing(ARRAY['contacts']);
    RAISE NOTICE '4. Contacts Only: $%.2f (Plan: %)', pricing_result.optimal_price_cents::DECIMAL / 100, pricing_result.optimal_plan_id;
    
    SELECT * INTO pricing_result FROM public.calculate_optimal_pricing(ARRAY['messages']);
    RAISE NOTICE '   Messages Only: $%.2f (Plan: %)', pricing_result.optimal_price_cents::DECIMAL / 100, pricing_result.optimal_plan_id;
    
    SELECT * INTO pricing_result FROM public.calculate_optimal_pricing(ARRAY['photos']);
    RAISE NOTICE '   Photos Only: $%.2f (Plan: %)', pricing_result.optimal_price_cents::DECIMAL / 100, pricing_result.optimal_plan_id;
END $$;

-- Test combination pricing
DO $$
DECLARE
    pricing_result RECORD;
BEGIN
    SELECT * INTO pricing_result FROM public.calculate_optimal_pricing(ARRAY['contacts', 'messages']);
    RAISE NOTICE '5. Contacts + Messages: $%.2f (Savings: $%.2f)', 
        pricing_result.optimal_price_cents::DECIMAL / 100, 
        pricing_result.savings_cents::DECIMAL / 100;
    
    SELECT * INTO pricing_result FROM public.calculate_optimal_pricing(ARRAY['contacts', 'messages', 'photos']);
    RAISE NOTICE '   Complete Backup: $%.2f (Savings: $%.2f)', 
        pricing_result.optimal_price_cents::DECIMAL / 100, 
        pricing_result.savings_cents::DECIMAL / 100;
END $$;

-- ============================================================================
-- STEP 3: Test user migration (if users exist)
-- ============================================================================

DO $$
DECLARE
    migrated_users INTEGER;
    total_users INTEGER;
BEGIN
    SELECT COUNT(*) INTO migrated_users FROM public.user_subscriptions WHERE legacy_tier_id IS NOT NULL;
    SELECT COUNT(*) INTO total_users FROM public.user_subscription_backup;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== USER MIGRATION VERIFICATION ===';
    RAISE NOTICE '6. User Migration: % of % users migrated successfully', migrated_users, total_users;
    
    IF total_users > 0 AND migrated_users != total_users THEN
        RAISE WARNING 'Not all users were migrated successfully';
    END IF;
END $$;

-- Check service selections were created
DO $$
DECLARE
    selection_count INTEGER;
    subscription_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO selection_count FROM public.user_service_selections WHERE is_active = TRUE;
    SELECT COUNT(*) INTO subscription_count FROM public.user_subscriptions;
    
    RAISE NOTICE '7. Service Selections: % active selections for % subscriptions', selection_count, subscription_count;
END $$;

-- ============================================================================
-- STEP 4: Test RLS policies
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING SECURITY POLICIES ===';
END $$;

-- Check RLS is enabled
DO $$
DECLARE
    rls_tables TEXT[];
BEGIN
    SELECT array_agg(tablename) INTO rls_tables
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND t.tablename IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections', 'user_subscriptions')
    AND c.relrowsecurity = TRUE;
    
    RAISE NOTICE '8. RLS Enabled: % tables have Row Level Security enabled', array_length(rls_tables, 1);
    
    IF array_length(rls_tables, 1) < 5 THEN
        RAISE WARNING 'Not all required tables have RLS enabled';
    END IF;
END $$;

-- ============================================================================
-- STEP 5: Performance tests
-- ============================================================================

DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== PERFORMANCE TESTING ===';
    
    -- Test pricing calculation performance
    start_time := clock_timestamp();
    PERFORM public.calculate_optimal_pricing(ARRAY['contacts', 'messages', 'photos']) FROM generate_series(1, 100);
    end_time := clock_timestamp();
    duration := end_time - start_time;
    
    RAISE NOTICE '9. Performance: 100 pricing calculations completed in %', duration;
    
    IF EXTRACT(EPOCH FROM duration) > 1 THEN
        RAISE WARNING 'Pricing calculations may be slower than expected';
    END IF;
END $$;

-- ============================================================================
-- STEP 6: Data integrity checks
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== DATA INTEGRITY CHECKS ===';
END $$;

-- Check for orphaned records
DO $$
DECLARE
    orphaned_plan_services INTEGER;
    orphaned_user_selections INTEGER;
BEGIN
    -- Check plan_services without valid plans
    SELECT COUNT(*) INTO orphaned_plan_services
    FROM public.plan_services ps
    LEFT JOIN public.subscription_plans sp ON ps.plan_id = sp.id
    WHERE sp.id IS NULL;
    
    -- Check user_service_selections without valid services
    SELECT COUNT(*) INTO orphaned_user_selections
    FROM public.user_service_selections uss
    LEFT JOIN public.service_types st ON uss.service_type_id = st.id
    WHERE st.id IS NULL;
    
    RAISE NOTICE '10. Data Integrity: % orphaned plan services, % orphaned user selections', 
        orphaned_plan_services, orphaned_user_selections;
    
    IF orphaned_plan_services > 0 OR orphaned_user_selections > 0 THEN
        RAISE WARNING 'Found orphaned records that need cleanup';
    END IF;
END $$;

-- ============================================================================
-- STEP 7: Generate sample usage report
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SAMPLE PRICING SCENARIOS ===';
END $$;

-- Show all available plans with pricing
SELECT 
    '11. Available Plans:' as section,
    sp.id,
    sp.name,
    '$' || (sp.price_cents::DECIMAL / 100)::TEXT as price,
    sp.total_storage_gb || 'GB' as storage,
    CASE WHEN sp.is_combination THEN 'Combination' ELSE 'Individual' END as type,
    CASE WHEN sp.is_popular THEN 'POPULAR' ELSE '' END as badge
FROM public.subscription_plans sp
WHERE sp.is_active = TRUE
ORDER BY sp.sort_order;

-- Show service combination pricing
SELECT 
    '12. Service Combinations:' as section,
    scp.service_names,
    '$' || (scp.individual_total_cents::DECIMAL / 100)::TEXT as individual_cost,
    '$' || (scp.best_price_cents::DECIMAL / 100)::TEXT as best_price,
    '$' || (scp.savings_cents::DECIMAL / 100)::TEXT as savings,
    scp.recommended_plan_name
FROM public.service_combination_pricing scp
ORDER BY array_length(scp.service_ids, 1), scp.best_price_cents;

-- ============================================================================
-- STEP 8: Final verification summary
-- ============================================================================

DO $$
DECLARE
    verification_passed BOOLEAN := TRUE;
    error_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== VERIFICATION SUMMARY ===';
    
    -- Count any warnings or errors from previous checks
    -- This is a simplified check - in production you'd want more detailed validation
    
    -- Check critical components exist
    IF NOT EXISTS (SELECT 1 FROM public.service_types WHERE id = 'contacts') THEN
        verification_passed := FALSE;
        error_count := error_count + 1;
        RAISE WARNING 'Missing contacts service type';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM public.subscription_plans WHERE id = 'complete-backup') THEN
        verification_passed := FALSE;
        error_count := error_count + 1;
        RAISE WARNING 'Missing complete backup plan';
    END IF;
    
    -- Final result
    IF verification_passed THEN
        RAISE NOTICE '✅ VERIFICATION PASSED: Modular pricing migration completed successfully!';
        RAISE NOTICE 'All systems ready for production use.';
    ELSE
        RAISE NOTICE '❌ VERIFICATION FAILED: % errors found', error_count;
        RAISE NOTICE 'Please review and fix issues before proceeding.';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Migration verification completed at %', NOW();
    RAISE NOTICE '================================================';
END $$;
