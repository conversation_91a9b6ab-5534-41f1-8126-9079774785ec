import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';
import { useServiceAccess, useSpecificServiceAccess } from '../hooks/useServiceAccess';
import ServiceAccessGuard from './ServiceAccessGuard';
import UpgradePrompt from './UpgradePrompt';
import SubscriptionStatusCard from './SubscriptionStatusCard';

/**
 * Example component demonstrating how to use the service access system
 * This shows various patterns for implementing feature access control
 */
const ServiceAccessExample: React.FC = () => {
  const navigation = useNavigation();
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);
  const [selectedService, setSelectedService] = useState<'contacts' | 'messages' | 'photos'>('contacts');

  // Hook for overall service access
  const {
    serviceAccess,
    subscription,
    isLoading,
    hasContactsAccess,
    hasMessagesAccess,
    hasPhotosAccess,
    hasAnyAccess,
    validateOperation,
    refreshAccess,
  } = useServiceAccess();

  // Hook for specific service access (example with photos)
  const {
    hasAccess: hasPhotosAccessSpecific,
    accessResult: photosAccessResult,
    isLoading: isLoadingPhotosAccess,
    showUpgradePrompt: showPhotosUpgradePrompt,
    validateOperation: validatePhotosOperation,
  } = useSpecificServiceAccess('photos');

  // Example: Validate operation before performing action
  const handleBackupPhotos = async () => {
    const canBackup = await validateOperation('photos', 'backup photos');
    if (canBackup) {
      // Perform backup operation
      console.log('Starting photos backup...');
      // BackupService.backupPhotos();
    }
  };

  // Example: Check access and show custom prompt
  const handleAccessRestrictedFeature = async (serviceId: 'contacts' | 'messages' | 'photos') => {
    const canAccess = await validateOperation(serviceId, 'access feature', false);
    if (!canAccess) {
      setSelectedService(serviceId);
      setShowUpgradePrompt(true);
    } else {
      // Feature is accessible
      console.log(`Accessing ${serviceId} feature...`);
    }
  };

  const handleUpgrade = () => {
    setShowUpgradePrompt(false);
    navigation.navigate('ModularPricing' as never);
  };

  const renderServiceStatus = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text style={styles.cardTitle}>Service Access Status</Text>
        
        <View style={styles.serviceList}>
          <View style={styles.serviceItem}>
            <Icon 
              name="account-multiple" 
              size={24} 
              color={hasContactsAccess ? COLORS.success : COLORS.textSecondary} 
            />
            <Text style={styles.serviceName}>Contacts</Text>
            <Chip 
              style={[styles.statusChip, hasContactsAccess ? styles.activeChip : styles.inactiveChip]}
              textStyle={hasContactsAccess ? styles.activeChipText : styles.inactiveChipText}
            >
              {hasContactsAccess ? 'Active' : 'Locked'}
            </Chip>
          </View>

          <View style={styles.serviceItem}>
            <Icon 
              name="message-text" 
              size={24} 
              color={hasMessagesAccess ? COLORS.success : COLORS.textSecondary} 
            />
            <Text style={styles.serviceName}>Messages</Text>
            <Chip 
              style={[styles.statusChip, hasMessagesAccess ? styles.activeChip : styles.inactiveChip]}
              textStyle={hasMessagesAccess ? styles.activeChipText : styles.inactiveChipText}
            >
              {hasMessagesAccess ? 'Active' : 'Locked'}
            </Chip>
          </View>

          <View style={styles.serviceItem}>
            <Icon 
              name="camera" 
              size={24} 
              color={hasPhotosAccess ? COLORS.success : COLORS.textSecondary} 
            />
            <Text style={styles.serviceName}>Photos</Text>
            <Chip 
              style={[styles.statusChip, hasPhotosAccess ? styles.activeChip : styles.inactiveChip]}
              textStyle={hasPhotosAccess ? styles.activeChipText : styles.inactiveChipText}
            >
              {hasPhotosAccess ? 'Active' : 'Locked'}
            </Chip>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderActionButtons = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text style={styles.cardTitle}>Feature Access Examples</Text>
        
        <View style={styles.buttonGroup}>
          <Button
            mode="contained"
            onPress={handleBackupPhotos}
            style={styles.actionButton}
            icon="camera"
          >
            Backup Photos (Validated)
          </Button>

          <Button
            mode="outlined"
            onPress={() => handleAccessRestrictedFeature('contacts')}
            style={styles.actionButton}
            icon="account-multiple"
          >
            Access Contacts (Custom Prompt)
          </Button>

          <Button
            mode="outlined"
            onPress={() => handleAccessRestrictedFeature('messages')}
            style={styles.actionButton}
            icon="message-text"
          >
            Access Messages (Custom Prompt)
          </Button>

          <Button
            mode="text"
            onPress={refreshAccess}
            style={styles.actionButton}
            icon="refresh"
          >
            Refresh Access Status
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderGuardedContent = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text style={styles.cardTitle}>Service Access Guards</Text>
        <Text style={styles.cardDescription}>
          These components are wrapped with ServiceAccessGuard and will show upgrade prompts if access is denied.
        </Text>
        
        <View style={styles.guardedSection}>
          <ServiceAccessGuard serviceId="contacts">
            <View style={styles.protectedContent}>
              <Icon name="account-multiple" size={32} color={COLORS.success} />
              <Text style={styles.protectedText}>Contacts Feature Unlocked!</Text>
              <Text style={styles.protectedDescription}>
                You have access to contacts backup and sync.
              </Text>
            </View>
          </ServiceAccessGuard>
        </View>

        <View style={styles.guardedSection}>
          <ServiceAccessGuard serviceId="messages">
            <View style={styles.protectedContent}>
              <Icon name="message-text" size={32} color={COLORS.success} />
              <Text style={styles.protectedText}>Messages Feature Unlocked!</Text>
              <Text style={styles.protectedDescription}>
                You have access to messages backup and search.
              </Text>
            </View>
          </ServiceAccessGuard>
        </View>

        <View style={styles.guardedSection}>
          <ServiceAccessGuard serviceId="photos">
            <View style={styles.protectedContent}>
              <Icon name="camera" size={32} color={COLORS.success} />
              <Text style={styles.protectedText}>Photos Feature Unlocked!</Text>
              <Text style={styles.protectedDescription}>
                You have access to photos backup and organization.
              </Text>
            </View>
          </ServiceAccessGuard>
        </View>
      </Card.Content>
    </Card>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading service access...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Subscription Status */}
      <SubscriptionStatusCard />

      {/* Service Status */}
      {renderServiceStatus()}

      {/* Action Buttons */}
      {renderActionButtons()}

      {/* Guarded Content */}
      {renderGuardedContent()}

      {/* Custom Upgrade Prompt */}
      <UpgradePrompt
        visible={showUpgradePrompt}
        serviceId={selectedService}
        onUpgrade={handleUpgrade}
        onCancel={() => setShowUpgradePrompt(false)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    marginBottom: SPACING.lg,
    elevation: 2,
  },
  cardTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  cardDescription: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  serviceList: {
    gap: SPACING.md,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  serviceName: {
    flex: 1,
    fontSize: FONTS.sizes.medium,
    color: COLORS.text,
    marginLeft: SPACING.md,
  },
  statusChip: {
    minWidth: 80,
  },
  activeChip: {
    backgroundColor: COLORS.success,
  },
  inactiveChip: {
    backgroundColor: COLORS.border,
  },
  activeChipText: {
    color: '#FFFFFF',
  },
  inactiveChipText: {
    color: COLORS.textSecondary,
  },
  buttonGroup: {
    gap: SPACING.md,
  },
  actionButton: {
    marginBottom: SPACING.sm,
  },
  guardedSection: {
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  protectedContent: {
    alignItems: 'center',
    padding: SPACING.lg,
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
  },
  protectedText: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.success,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  protectedDescription: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default ServiceAccessExample;
