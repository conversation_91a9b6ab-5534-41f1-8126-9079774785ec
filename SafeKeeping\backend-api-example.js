// SafeKeep Backend API Example - Stripe Integration
// This file shows how to safely handle Stripe payments on your backend server
// IMPORTANT: This should run on your server, NOT in the React Native app

const express = require('express');
const cors = require('cors');
const Stripe = require('stripe');
require('dotenv').config(); // Load environment variables from .env file

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Stripe with secret key (ONLY on backend!)
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// Middleware
app.use(cors());
app.use(express.json());

// Webhook middleware for raw body (needed for Stripe webhook verification)
app.use('/webhook', express.raw({ type: 'application/json' }));

// SafeKeep modular subscription plans (should match frontend)
const SAFEKEEP_PLANS = {
  // Individual Services
  CONTACTS_ONLY: {
    id: 'contacts_only',
    name: 'Contacts Only',
    price: 99, // $0.99/month in cents
    currency: 'usd',
    storageGB: 1,
    services: ['contacts']
  },
  MESSAGES_ONLY: {
    id: 'messages_only',
    name: 'Messages Only',
    price: 199, // $1.99/month in cents
    currency: 'usd',
    storageGB: 1,
    services: ['messages']
  },
  PHOTOS_ONLY: {
    id: 'photos_only',
    name: 'Photos Only',
    price: 499, // $4.99/month in cents
    currency: 'usd',
    storageGB: 10,
    services: ['photos']
  },
  // Combination Services
  CONTACTS_MESSAGES: {
    id: 'contacts_messages',
    name: 'Contacts + Messages',
    price: 249, // $2.49/month in cents
    currency: 'usd',
    storageGB: 2,
    services: ['contacts', 'messages']
  },
  CONTACTS_PHOTOS: {
    id: 'contacts_photos',
    name: 'Contacts + Photos',
    price: 549, // $5.49/month in cents
    currency: 'usd',
    storageGB: 11,
    services: ['contacts', 'photos']
  },
  MESSAGES_PHOTOS: {
    id: 'messages_photos',
    name: 'Messages + Photos',
    price: 649, // $6.49/month in cents
    currency: 'usd',
    storageGB: 11,
    services: ['messages', 'photos']
  },
  COMPLETE_BACKUP: {
    id: 'complete_backup',
    name: 'Complete Backup',
    price: 699, // $6.99/month in cents
    currency: 'usd',
    storageGB: 12,
    services: ['contacts', 'messages', 'photos']
  },
};

// API Routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    service: 'SafeKeep Payment API',
    timestamp: new Date().toISOString()
  });
});

// Create Payment Intent (called from React Native app)
app.post('/api/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency = 'usd', customerId, description } = req.body;

    // Validate input
    if (!amount || amount < 50) { // Minimum $0.50
      return res.status(400).json({ 
        error: 'Invalid amount. Minimum $0.50 required.' 
      });
    }

    console.log('💳 Creating payment intent:', { amount, currency, description });

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount), // Ensure integer
      currency,
      customer: customerId,
      description: description || 'SafeKeep Subscription',
      metadata: {
        app: 'SafeKeep',
        environment: process.env.NODE_ENV || 'development',
        created_at: new Date().toISOString(),
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    console.log('✅ Payment intent created:', paymentIntent.id);

    // Return client secret to frontend (safe to send)
    res.json({
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
    });

  } catch (error) {
    console.error('❌ Payment intent creation failed:', error);
    res.status(500).json({ 
      error: 'Payment intent creation failed',
      message: error.message 
    });
  }
});

// Create Customer
app.post('/api/create-customer', async (req, res) => {
  try {
    const { email, name } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    console.log('👤 Creating customer:', { email, name });

    // Check if customer already exists
    const existingCustomers = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      console.log('✅ Customer already exists:', existingCustomers.data[0].id);
      return res.json({ customer: existingCustomers.data[0] });
    }

    // Create new customer
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        app: 'SafeKeep',
        created_at: new Date().toISOString(),
      },
    });

    console.log('✅ Customer created:', customer.id);
    res.json({ customer });

  } catch (error) {
    console.error('❌ Customer creation failed:', error);
    res.status(500).json({ 
      error: 'Customer creation failed',
      message: error.message 
    });
  }
});

// Get subscription plans
app.get('/api/plans', (req, res) => {
  res.json({ plans: SAFEKEEP_PLANS });
});

// Stripe Webhook Handler (CRITICAL for production)
app.post('/webhook', (req, res) => {
  const sig = req.headers['stripe-signature'];
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    console.error('❌ Webhook secret not configured');
    return res.status(500).send('Webhook secret not configured');
  }

  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    console.log('✅ Webhook signature verified:', event.type);
  } catch (err) {
    console.error('❌ Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('💰 Payment succeeded:', paymentIntent.id);
      
      // TODO: Update your database, send confirmation email, etc.
      // Example: activateUserSubscription(paymentIntent.customer);
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('❌ Payment failed:', failedPayment.id);
      
      // TODO: Handle failed payment, notify user, etc.
      break;

    case 'customer.subscription.created':
      const subscription = event.data.object;
      console.log('🔄 Subscription created:', subscription.id);
      
      // TODO: Activate user's subscription features
      break;

    case 'customer.subscription.deleted':
      const canceledSubscription = event.data.object;
      console.log('❌ Subscription canceled:', canceledSubscription.id);
      
      // TODO: Deactivate user's subscription features
      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  res.json({ received: true });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SafeKeep Payment API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`💳 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Validate Stripe configuration
  if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER')) {
    console.warn('⚠️ WARNING: Stripe secret key not properly configured!');
  } else {
    console.log('✅ Stripe configuration loaded');
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Server shutting down gracefully...');
  process.exit(0);
});

module.exports = app;
