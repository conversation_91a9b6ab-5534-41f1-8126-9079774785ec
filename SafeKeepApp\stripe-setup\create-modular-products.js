#!/usr/bin/env node

/**
 * SafeKeep Modular Pricing - Stripe Product Creation Script
 * 
 * This script creates all necessary Stripe products and prices for the modular pricing structure.
 * Run this script once to set up your Stripe account with all the required products.
 * 
 * Usage: node create-modular-products.js
 * 
 * Make sure to set your STRIPE_SECRET_KEY environment variable before running.
 */

const Stripe = require('stripe');

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_your_key_here');

// Modular pricing structure
const MODULAR_PRODUCTS = {
  // Individual Services
  'contacts-only': {
    name: 'Contacts Backup',
    description: 'Secure backup and sync of your contacts with deduplication and cross-device sync',
    price: 99, // $0.99
    features: ['Contact deduplication', 'Cross-device sync', 'Backup history', 'Export options'],
    storage: '1GB',
    services: ['contacts']
  },
  'messages-only': {
    name: 'Messages Backup',
    description: 'Secure backup and sync of your messages and SMS with threading and search',
    price: 199, // $1.99
    features: ['SMS & MMS backup', 'Message threading', 'Search functionality', 'Media attachments'],
    storage: '2GB',
    services: ['messages']
  },
  'photos-only': {
    name: 'Photos Backup',
    description: 'Secure backup and sync of your photos and media with smart compression',
    price: 499, // $4.99
    features: ['Automatic photo backup', 'Smart compression', 'Album organization', 'Face recognition'],
    storage: '10GB',
    services: ['photos']
  },
  
  // Combination Plans
  'contacts-messages': {
    name: 'Contacts + Messages',
    description: 'Essential communication backup bundle with contacts and messages',
    price: 249, // $2.49 (save $0.49)
    features: ['All contacts features', 'All messages features', 'Bundle savings'],
    storage: '3GB',
    services: ['contacts', 'messages'],
    savings: 49
  },
  'contacts-photos': {
    name: 'Contacts + Photos',
    description: 'Personal memories and connections bundle with contacts and photos',
    price: 549, // $5.49 (save $0.49)
    features: ['All contacts features', 'All photos features', 'Bundle savings'],
    storage: '11GB',
    services: ['contacts', 'photos'],
    savings: 49
  },
  'messages-photos': {
    name: 'Messages + Photos',
    description: 'Complete personal data bundle with messages and photos',
    price: 649, // $6.49 (save $0.49)
    features: ['All messages features', 'All photos features', 'Bundle savings'],
    storage: '12GB',
    services: ['messages', 'photos'],
    savings: 49,
    isPopular: true
  },
  'complete-backup': {
    name: 'Complete Backup',
    description: 'All services included - best value for complete data protection',
    price: 699, // $6.99 (save $0.98)
    features: ['All contacts features', 'All messages features', 'All photos features', 'Maximum savings'],
    storage: '13GB',
    services: ['contacts', 'messages', 'photos'],
    savings: 98,
    isPopular: true
  }
};

async function createModularProducts() {
  console.log('🚀 Creating SafeKeep Modular Pricing Products in Stripe...\n');

  const results = {
    products: [],
    prices: [],
    errors: []
  };

  for (const [planId, productData] of Object.entries(MODULAR_PRODUCTS)) {
    try {
      console.log(`📦 Creating product: ${productData.name}`);

      // Create Stripe Product
      const product = await stripe.products.create({
        name: productData.name,
        description: productData.description,
        metadata: {
          plan_id: planId,
          services: productData.services.join(','),
          storage: productData.storage,
          savings: productData.savings?.toString() || '0',
          is_popular: productData.isPopular?.toString() || 'false',
          features: JSON.stringify(productData.features)
        },
        images: [], // Add product images if you have them
        url: 'https://safekeep.com', // Your product URL
      });

      console.log(`✅ Product created: ${product.id}`);

      // Create Stripe Price for monthly billing
      const price = await stripe.prices.create({
        product: product.id,
        unit_amount: productData.price,
        currency: 'usd',
        recurring: {
          interval: 'month',
        },
        metadata: {
          plan_id: planId,
          services: productData.services.join(','),
          billing_descriptor: `SafeKeep ${productData.name}`,
        },
        nickname: `${productData.name} - Monthly`,
      });

      console.log(`💰 Price created: ${price.id} ($${(productData.price / 100).toFixed(2)}/month)`);

      results.products.push({
        planId,
        productId: product.id,
        priceId: price.id,
        name: productData.name,
        price: productData.price
      });

      results.prices.push(price.id);

    } catch (error) {
      console.error(`❌ Error creating product ${planId}:`, error.message);
      results.errors.push({
        planId,
        error: error.message
      });
    }

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return results;
}

async function generateConfigFile(results) {
  const config = {
    // Product and Price IDs for your application
    STRIPE_PRODUCTS: {},
    STRIPE_PRICES: {},
    
    // Environment variables for your backend
    ENV_VARIABLES: {},
    
    // Webhook events to listen for
    WEBHOOK_EVENTS: [
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
      'invoice.payment_failed',
      'customer.created',
      'customer.updated'
    ]
  };

  results.products.forEach(product => {
    config.STRIPE_PRODUCTS[product.planId.toUpperCase().replace('-', '_')] = product.productId;
    config.STRIPE_PRICES[product.planId.toUpperCase().replace('-', '_')] = product.priceId;
    config.ENV_VARIABLES[`STRIPE_PRICE_${product.planId.toUpperCase().replace('-', '_')}`] = product.priceId;
  });

  console.log('\n📋 Configuration for your application:');
  console.log('=====================================');
  console.log('\n// Add these to your environment variables:');
  Object.entries(config.ENV_VARIABLES).forEach(([key, value]) => {
    console.log(`${key}=${value}`);
  });

  console.log('\n// Product IDs for your code:');
  console.log('export const STRIPE_PRODUCTS = {');
  Object.entries(config.STRIPE_PRODUCTS).forEach(([key, value]) => {
    console.log(`  ${key}: '${value}',`);
  });
  console.log('};');

  console.log('\n// Price IDs for your code:');
  console.log('export const STRIPE_PRICES = {');
  Object.entries(config.STRIPE_PRICES).forEach(([key, value]) => {
    console.log(`  ${key}: '${value}',`);
  });
  console.log('};');

  return config;
}

async function main() {
  try {
    // Verify Stripe connection
    console.log('🔍 Verifying Stripe connection...');
    await stripe.accounts.retrieve();
    console.log('✅ Stripe connection verified\n');

    // Create products and prices
    const results = await createModularProducts();

    // Generate configuration
    await generateConfigFile(results);

    // Summary
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`✅ Products created: ${results.products.length}`);
    console.log(`💰 Prices created: ${results.prices.length}`);
    console.log(`❌ Errors: ${results.errors.length}`);

    if (results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      results.errors.forEach(error => {
        console.log(`  - ${error.planId}: ${error.error}`);
      });
    }

    console.log('\n🎉 Stripe setup complete!');
    console.log('\nNext steps:');
    console.log('1. Copy the environment variables to your .env file');
    console.log('2. Update your application code with the product/price IDs');
    console.log('3. Configure webhook endpoints in your Stripe dashboard');
    console.log('4. Test the payment flow with Stripe test cards');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = { createModularProducts, MODULAR_PRODUCTS };
