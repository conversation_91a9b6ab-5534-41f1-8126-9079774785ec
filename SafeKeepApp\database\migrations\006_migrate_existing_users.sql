-- SafeKeep Existing User Migration
-- Migration 006: Migrate existing users to modular pricing structure
-- Run this script after 005_populate_modular_pricing_data.sql

-- ============================================================================
-- STEP 1: Backup existing subscription data
-- ============================================================================

-- Create backup table for existing user subscription data
CREATE TABLE IF NOT EXISTS public.user_subscription_backup AS
SELECT 
    id,
    email,
    subscription_tier,
    subscription_status,
    subscription_expires_at,
    stripe_customer_id,
    storage_used,
    storage_quota,
    created_at,
    last_login_at
FROM public.users
WHERE subscription_tier IS NOT NULL;

-- Log the backup
DO $$
BEGIN
    RAISE NOTICE 'Backed up % existing user subscriptions', (SELECT COUNT(*) FROM public.user_subscription_backup);
END $$;

-- ============================================================================
-- STEP 2: Migrate existing users to new subscription structure
-- ============================================================================

-- Insert user subscriptions based on existing subscription_tier
INSERT INTO public.user_subscriptions (
    user_id,
    plan_id,
    legacy_tier_id,
    status,
    total_price_cents,
    service_combination,
    stripe_customer_id,
    current_period_start,
    current_period_end,
    created_at,
    updated_at
)
SELECT 
    u.id as user_id,
    ltm.equivalent_plan_id as plan_id,
    u.subscription_tier as legacy_tier_id,
    CASE 
        WHEN u.subscription_status = 'active' THEN 'active'
        WHEN u.subscription_status = 'cancelled' THEN 'canceled'
        WHEN u.subscription_status = 'expired' THEN 'canceled'
        WHEN u.subscription_status = 'past_due' THEN 'past_due'
        ELSE 'active'
    END as status,
    sp.price_cents as total_price_cents,
    (
        SELECT jsonb_agg(ps.service_type_id ORDER BY ps.service_type_id)
        FROM public.plan_services ps 
        WHERE ps.plan_id = ltm.equivalent_plan_id AND ps.is_included = TRUE
    ) as service_combination,
    u.stripe_customer_id,
    COALESCE(u.subscription_expires_at - INTERVAL '1 month', u.created_at) as current_period_start,
    COALESCE(u.subscription_expires_at, u.created_at + INTERVAL '1 month') as current_period_end,
    u.created_at,
    NOW() as updated_at
FROM public.users u
JOIN public.legacy_tier_mapping ltm ON u.subscription_tier = ltm.legacy_tier
JOIN public.subscription_plans sp ON ltm.equivalent_plan_id = sp.id
WHERE u.subscription_tier IS NOT NULL
ON CONFLICT (user_id) DO UPDATE SET
    plan_id = EXCLUDED.plan_id,
    legacy_tier_id = EXCLUDED.legacy_tier_id,
    status = EXCLUDED.status,
    total_price_cents = EXCLUDED.total_price_cents,
    service_combination = EXCLUDED.service_combination,
    updated_at = NOW();

-- ============================================================================
-- STEP 3: Create user service selections based on migrated subscriptions
-- ============================================================================

-- Insert user service selections based on their subscription plan
INSERT INTO public.user_service_selections (
    user_id,
    service_type_id,
    is_active,
    activated_at
)
SELECT DISTINCT
    us.user_id,
    ps.service_type_id,
    TRUE as is_active,
    us.created_at as activated_at
FROM public.user_subscriptions us
JOIN public.plan_services ps ON us.plan_id = ps.plan_id
WHERE ps.is_included = TRUE
ON CONFLICT (user_id, service_type_id) DO UPDATE SET
    is_active = TRUE,
    activated_at = EXCLUDED.activated_at,
    updated_at = NOW();

-- ============================================================================
-- STEP 4: Update users table with new modular pricing fields
-- ============================================================================

-- Update users table with selected services and subscription plan
UPDATE public.users 
SET 
    selected_services = us.service_combination,
    subscription_plan_id = us.plan_id,
    total_subscription_cost_cents = us.total_price_cents
FROM public.user_subscriptions us
WHERE public.users.id = us.user_id;

-- ============================================================================
-- STEP 5: Migrate storage usage data to service-specific tracking
-- ============================================================================

-- Update storage_usage with estimated service-specific data
-- This is a best-effort migration based on typical usage patterns
UPDATE public.storage_usage su
SET 
    contacts_count = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'contacts' AND uss.is_active = TRUE
        ) THEN GREATEST(50, LEAST(500, (su.total_size_bytes / 1024 / 1024)::INTEGER)) -- Estimate 50-500 contacts
        ELSE 0
    END,
    messages_count = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'messages' AND uss.is_active = TRUE
        ) THEN GREATEST(100, LEAST(10000, (su.total_size_bytes / 1024)::INTEGER)) -- Estimate based on storage
        ELSE 0
    END,
    photos_count = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'photos' AND uss.is_active = TRUE
        ) THEN GREATEST(10, LEAST(5000, (su.total_size_bytes / 1024 / 1024 / 2)::INTEGER)) -- Estimate ~2MB per photo
        ELSE 0
    END,
    contacts_size_bytes = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'contacts' AND uss.is_active = TRUE
        ) THEN LEAST(su.total_size_bytes * 0.05, 50 * 1024 * 1024)::BIGINT -- Max 5% or 50MB
        ELSE 0
    END,
    messages_size_bytes = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'messages' AND uss.is_active = TRUE
        ) THEN LEAST(su.total_size_bytes * 0.15, 500 * 1024 * 1024)::BIGINT -- Max 15% or 500MB
        ELSE 0
    END,
    photos_size_bytes = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'photos' AND uss.is_active = TRUE
        ) THEN GREATEST(su.total_size_bytes * 0.8, su.total_size_bytes - 550 * 1024 * 1024)::BIGINT -- Remaining after contacts/messages
        ELSE 0
    END,
    last_service_sync = jsonb_build_object(
        'contacts', CASE WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'contacts' AND uss.is_active = TRUE
        ) THEN su.last_updated ELSE NULL END,
        'messages', CASE WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'messages' AND uss.is_active = TRUE
        ) THEN su.last_updated ELSE NULL END,
        'photos', CASE WHEN EXISTS (
            SELECT 1 FROM public.user_service_selections uss 
            WHERE uss.user_id = su.user_id AND uss.service_type_id = 'photos' AND uss.is_active = TRUE
        ) THEN su.last_updated ELSE NULL END
    );

-- ============================================================================
-- STEP 6: Create migration summary and validation
-- ============================================================================

-- Create migration summary table
CREATE TABLE IF NOT EXISTS public.migration_summary (
    id SERIAL PRIMARY KEY,
    migration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_users_migrated INTEGER,
    subscriptions_created INTEGER,
    service_selections_created INTEGER,
    legacy_basic_users INTEGER,
    legacy_premium_users INTEGER,
    legacy_family_users INTEGER,
    validation_passed BOOLEAN DEFAULT FALSE
);

-- Insert migration summary
INSERT INTO public.migration_summary (
    total_users_migrated,
    subscriptions_created,
    service_selections_created,
    legacy_basic_users,
    legacy_premium_users,
    legacy_family_users
)
SELECT 
    (SELECT COUNT(*) FROM public.user_subscription_backup) as total_users_migrated,
    (SELECT COUNT(*) FROM public.user_subscriptions WHERE legacy_tier_id IS NOT NULL) as subscriptions_created,
    (SELECT COUNT(*) FROM public.user_service_selections) as service_selections_created,
    (SELECT COUNT(*) FROM public.user_subscription_backup WHERE subscription_tier = 'basic') as legacy_basic_users,
    (SELECT COUNT(*) FROM public.user_subscription_backup WHERE subscription_tier = 'premium') as legacy_premium_users,
    (SELECT COUNT(*) FROM public.user_subscription_backup WHERE subscription_tier = 'family') as legacy_family_users;

-- Validation and success message
DO $$
DECLARE
    migration_record RECORD;
BEGIN
    SELECT * INTO migration_record FROM public.migration_summary ORDER BY id DESC LIMIT 1;
    
    RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'Total users migrated: %', migration_record.total_users_migrated;
    RAISE NOTICE 'New subscriptions created: %', migration_record.subscriptions_created;
    RAISE NOTICE 'Service selections created: %', migration_record.service_selections_created;
    RAISE NOTICE 'Legacy basic users: %', migration_record.legacy_basic_users;
    RAISE NOTICE 'Legacy premium users: %', migration_record.legacy_premium_users;
    RAISE NOTICE 'Legacy family users: %', migration_record.legacy_family_users;
    RAISE NOTICE '========================================';
    
    -- Update validation status
    UPDATE public.migration_summary 
    SET validation_passed = TRUE 
    WHERE id = migration_record.id;
END $$;
