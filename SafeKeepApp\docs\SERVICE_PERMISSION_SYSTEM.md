# SafeKeep Service Permission System Documentation

## Overview

The Service Permission System is a comprehensive access control framework that manages user permissions for SafeKeep's modular services (Contacts, Messages, Photos). It ensures users can only access features they've subscribed to while providing seamless upgrade experiences.

## 🎯 System Architecture

### Core Components

#### 1. **ServiceAccessService**
Central service for managing permissions and subscription validation.

```typescript
class ServiceAccessService {
  // Check specific service access
  async hasServiceAccess(serviceId: 'contacts' | 'messages' | 'photos'): Promise<AccessCheckResult>
  
  // Get all service permissions
  async getAllServiceAccess(): Promise<ServiceAccess>
  
  // Validate operation before execution
  async validateServiceOperation(serviceId: string, operation: string, showPrompt: boolean): Promise<boolean>
  
  // Show upgrade prompt for denied access
  async showUpgradePrompt(serviceId: string, onUpgrade?: () => void): Promise<void>
}
```

#### 2. **ServiceAccessGuard Component**
Higher-order component that automatically protects features requiring specific service access.

```typescript
<ServiceAccessGuard serviceId="photos">
  <PhotoBackupScreen />
</ServiceAccessGuard>
```

#### 3. **React Hooks**
Convenient hooks for accessing permissions in components.

```typescript
const { hasContactsAccess, hasMessagesAccess, hasPhotosAccess } = useServiceAccess();
```

## 🔐 Permission Levels

### Service Types
- **`contacts`**: Access to contacts backup and sync features
- **`messages`**: Access to messages backup and search features  
- **`photos`**: Access to photos backup and organization features

### Access States
- **`granted`**: User has active subscription for service
- **`denied`**: User doesn't have subscription for service
- **`expired`**: User's subscription has expired
- **`cancelled`**: User's subscription was cancelled

### Permission Reasons
- **`no_subscription`**: User has no active subscription
- **`service_not_included`**: Service not in current plan
- **`subscription_expired`**: Subscription has expired
- **`subscription_cancelled`**: Subscription was cancelled

## 🛡️ Access Control Implementation

### 1. **Feature-Level Protection**

#### Method 1: Validation Before Operations
```typescript
const handleBackupPhotos = async () => {
  const canBackup = await validateOperation('photos', 'backup photos');
  if (canBackup) {
    // Perform backup operation
    await PhotoBackupService.startBackup();
  }
  // Upgrade prompt shown automatically if access denied
};
```

#### Method 2: Component Wrapping
```typescript
<ServiceAccessGuard serviceId="photos">
  <PhotoBackupInterface />
</ServiceAccessGuard>
```

#### Method 3: Conditional Rendering
```typescript
const PhotosScreen = () => {
  const { hasPhotosAccess } = useServiceAccess();
  
  return (
    <View>
      {hasPhotosAccess ? (
        <PhotoBackupInterface />
      ) : (
        <UpgradePromptCard serviceId="photos" />
      )}
    </View>
  );
};
```

### 2. **Screen-Level Protection**

#### Navigation Guards
```typescript
const renderTabContent = () => {
  switch (selectedTab) {
    case 'photos':
      return (
        <ServiceAccessGuard serviceId="photos">
          <PhotoBackupScreen />
        </ServiceAccessGuard>
      );
  }
};
```

#### Tab Indicators
```typescript
const tabButtons = [
  {
    value: 'photos',
    label: serviceAccess?.photos ? 'Photos' : 'Photos 🔒',
    icon: 'camera',
  }
];
```

### 3. **API-Level Protection**

#### Backend Validation
```typescript
// Middleware for API endpoints
app.use('/api/photos', validateServiceAccess('photos'));

// In route handlers
const hasAccess = await checkUserServiceAccess(userId, 'photos');
if (!hasAccess) {
  return res.status(403).json({ error: 'Service access required' });
}
```

## 🎨 User Experience Patterns

### 1. **Graceful Access Denial**

#### Upgrade Prompts
When users attempt to access restricted features, they see beautiful upgrade prompts with:
- Service description and benefits
- Feature list with checkmarks
- Pricing information
- Security messaging
- Clear upgrade and dismiss options

#### Visual Indicators
- 🔒 Lock icons for restricted services
- ✅ Checkmarks for active services
- Grayed out UI for unavailable features
- Loading states during access checks

### 2. **Seamless Upgrade Flow**

#### From Access Denial to Purchase
```
Feature Access Attempt → Access Check → Denied → Upgrade Prompt → 
Pricing Screen → Service Selection → Payment → Access Granted
```

#### Immediate Access
After successful payment:
- Service access granted immediately
- UI updates to show unlocked features
- User can continue with original action
- Confirmation message displayed

### 3. **Subscription Management**

#### Current Status Display
```typescript
<SubscriptionStatusCard>
  - Plan name and pricing
  - Active services with chips
  - Next billing date
  - Storage usage
  - Manage subscription button
</SubscriptionStatusCard>
```

#### Service Modification
- Add services to existing subscription
- Remove services with appropriate warnings
- View pricing changes with prorations
- Confirm changes with clear messaging

## 🔄 Permission Caching Strategy

### Cache Management
- **Cache Duration**: 5 minutes for performance
- **Cache Keys**: User ID + service combination
- **Cache Invalidation**: On subscription changes
- **Fallback**: Default to "no access" on errors

### Cache Implementation
```typescript
class ServiceAccessService {
  private cachedAccess: ServiceAccess | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async getAllServiceAccess(): Promise<ServiceAccess> {
    // Check cache first
    if (this.cachedAccess && Date.now() < this.cacheExpiry) {
      return this.cachedAccess;
    }
    
    // Fetch fresh data
    const access = await this.fetchServiceAccess();
    
    // Update cache
    this.cachedAccess = access;
    this.cacheExpiry = Date.now() + this.CACHE_DURATION;
    
    return access;
  }
}
```

## 🚨 Security Considerations

### Client-Side Validation
- **Purpose**: User experience and UI state management
- **Limitation**: Not for security enforcement
- **Backup**: All operations validated server-side

### Server-Side Enforcement
- **API Protection**: All endpoints validate permissions
- **Database Queries**: Include permission checks
- **Operation Validation**: Check access before data operations

### Error Handling
- **Graceful Degradation**: Default to restricted access
- **Error Logging**: Comprehensive error tracking
- **Fallback UI**: Show appropriate fallback content

## 📊 Permission Matrix

### Service Combinations and Access

| Subscription Plan | Contacts | Messages | Photos | Storage |
|------------------|----------|----------|---------|---------|
| No Subscription | ❌ | ❌ | ❌ | 0GB |
| Contacts Only | ✅ | ❌ | ❌ | 1GB |
| Messages Only | ❌ | ✅ | ❌ | 2GB |
| Photos Only | ❌ | ❌ | ✅ | 10GB |
| Contacts + Messages | ✅ | ✅ | ❌ | 3GB |
| Contacts + Photos | ✅ | ❌ | ✅ | 11GB |
| Messages + Photos | ❌ | ✅ | ✅ | 12GB |
| Complete Backup | ✅ | ✅ | ✅ | 13GB |

### Feature-Level Permissions

| Feature | Required Service | Fallback Behavior |
|---------|-----------------|-------------------|
| Contact Backup | contacts | Show upgrade prompt |
| Contact Sync | contacts | Show upgrade prompt |
| Contact Export | contacts | Show upgrade prompt |
| Message Backup | messages | Show upgrade prompt |
| Message Search | messages | Show upgrade prompt |
| Message Threading | messages | Show upgrade prompt |
| Photo Backup | photos | Show upgrade prompt |
| Photo Organization | photos | Show upgrade prompt |
| Photo Compression | photos | Show upgrade prompt |

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('ServiceAccessService', () => {
  test('should grant access for subscribed service', async () => {
    const result = await serviceAccessService.hasServiceAccess('photos');
    expect(result.hasAccess).toBe(true);
  });

  test('should deny access for unsubscribed service', async () => {
    const result = await serviceAccessService.hasServiceAccess('photos');
    expect(result.hasAccess).toBe(false);
    expect(result.reason).toBe('service_not_included');
  });
});
```

### Integration Tests
```typescript
describe('ServiceAccessGuard', () => {
  test('should render children when access granted', () => {
    mockServiceAccess({ photos: true });
    const { getByText } = render(
      <ServiceAccessGuard serviceId="photos">
        <Text>Photo Feature</Text>
      </ServiceAccessGuard>
    );
    expect(getByText('Photo Feature')).toBeInTheDocument();
  });
});
```

### E2E Tests
- Test complete upgrade flow from access denial to purchase
- Verify service access changes after subscription updates
- Test permission caching and invalidation
- Validate cross-platform consistency

## 📈 Monitoring and Analytics

### Key Metrics
- **Access Denial Rate**: Percentage of feature access attempts that are denied
- **Upgrade Conversion**: Rate of upgrade prompt to purchase conversion
- **Permission Check Performance**: Response time for access validation
- **Cache Hit Rate**: Effectiveness of permission caching

### Tracking Events
```typescript
// Track access denials
analytics.track('service_access_denied', {
  serviceId: 'photos',
  reason: 'service_not_included',
  userId: user.id
});

// Track upgrade prompts
analytics.track('upgrade_prompt_shown', {
  serviceId: 'photos',
  currentPlan: 'messages-only',
  suggestedPlan: 'messages-photos'
});

// Track successful upgrades
analytics.track('service_upgrade_completed', {
  fromServices: ['messages'],
  toServices: ['messages', 'photos'],
  priceDifference: 450
});
```

## 🔧 Configuration

### Environment Variables
```bash
# Cache settings
SERVICE_ACCESS_CACHE_DURATION=300000  # 5 minutes
SERVICE_ACCESS_CACHE_ENABLED=true

# API settings
SERVICE_ACCESS_API_TIMEOUT=5000  # 5 seconds
SERVICE_ACCESS_RETRY_ATTEMPTS=3

# Feature flags
ENABLE_SERVICE_ACCESS_GUARDS=true
ENABLE_UPGRADE_PROMPTS=true
ENABLE_PERMISSION_CACHING=true
```

### Feature Flags
- **Service Access Guards**: Enable/disable automatic access control
- **Upgrade Prompts**: Enable/disable upgrade prompt display
- **Permission Caching**: Enable/disable permission result caching
- **Debug Mode**: Enable detailed logging for troubleshooting

This comprehensive permission system ensures secure, user-friendly access control while maintaining excellent performance and user experience.
