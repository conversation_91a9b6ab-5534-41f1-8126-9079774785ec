import { ModularSubscription, SubscriptionDetails, SubscriptionRequest } from '../types/modular-pricing';
import { supabase } from '../utils/database';
import { ServiceValidator } from './ServiceValidator';
import { PricingEngine } from './PricingEngine';
import { v4 as uuidv4 } from 'uuid';

export class SubscriptionManager {
  private serviceValidator: ServiceValidator;
  private pricingEngine: PricingEngine;

  constructor() {
    this.serviceValidator = new ServiceValidator();
    this.pricingEngine = new PricingEngine();
  }

  /**
   * Create a new subscription with service combination
   * Requirements: 1.1, 1.2, 1.3, 3.1, 3.2, 3.3
   */
  async createSubscription(subscriptionRequest: SubscriptionRequest): Promise<ModularSubscription> {
    try {
      // Validate input
      if (!subscriptionRequest.userId) {
        throw new Error('User ID is required');
      }
      if (!subscriptionRequest.serviceIds || subscriptionRequest.serviceIds.length === 0) {
        throw new Error('Service IDs are required');
      }

      // Requirement 1.2: Validate that the selected service combination is valid
      const validationResult = await this.serviceValidator.validateServiceCombination(subscriptionRequest.serviceIds);
      if (!validationResult.isValid) {
        throw new Error(`Invalid service combination: ${validationResult.errors.join(', ')}`);
      }

      // Check if user already has an active subscription
      const { data: existingSubscription, error: existingError } = await supabase
        .from('user_subscriptions')
        .select('id, status')
        .eq('user_id', subscriptionRequest.userId)
        .eq('status', 'active')
        .single();

      if (existingError && existingError.code !== 'PGRST116') { // PGRST116 = no rows found
        throw new Error(`Error checking existing subscription: ${existingError.message}`);
      }

      if (existingSubscription) {
        throw new Error('User already has an active subscription. Use updateSubscription to modify services.');
      }

      // Requirement 1.3: Create the subscription with the optimal pricing plan
      const pricingResult = await this.pricingEngine.calculateOptimalPrice(subscriptionRequest.serviceIds);
      
      const subscriptionId = uuidv4();
      const currentDate = new Date();
      const nextBillingDate = new Date(currentDate);
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);

      // Start transaction
      const { data: subscription, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .insert({
          id: subscriptionId,
          user_id: subscriptionRequest.userId,
          plan_id: pricingResult.recommendedPlanId,
          total_price_cents: pricingResult.priceCents,
          status: 'active',
          stripe_subscription_id: null, // Will be updated when payment is processed
          stripe_customer_id: subscriptionRequest.customerId || null,
          current_period_start: currentDate.toISOString(),
          current_period_end: nextBillingDate.toISOString(),
          created_at: currentDate.toISOString(),
          updated_at: currentDate.toISOString()
        })
        .select()
        .single();

      if (subscriptionError) {
        throw new Error(`Failed to create subscription: ${subscriptionError.message}`);
      }

      // Requirement 3.1: Store the selected services
      const updateServicesResult = await supabase.rpc('update_user_services', {
        user_uuid: subscriptionRequest.userId,
        new_service_ids: subscriptionRequest.serviceIds
      });

      if (updateServicesResult.error) {
        // Rollback subscription creation
        await supabase
          .from('user_subscriptions')
          .delete()
          .eq('id', subscriptionId);
        
        throw new Error(`Failed to update user services: ${updateServicesResult.error.message}`);
      }

      return {
        id: subscription.id,
        userId: subscription.user_id,
        planId: subscription.plan_id,
        serviceIds: subscriptionRequest.serviceIds,
        totalPriceCents: subscription.total_price_cents,
        status: subscription.status as 'active' | 'inactive' | 'cancelled' | 'past_due',
        stripeSubscriptionId: subscription.stripe_subscription_id,
        stripeCustomerId: subscription.stripe_customer_id,
        currentPeriodStart: new Date(subscription.current_period_start),
        currentPeriodEnd: new Date(subscription.current_period_end),
        createdAt: new Date(subscription.created_at),
        updatedAt: new Date(subscription.updated_at)
      };

    } catch (error) {
      console.error('SubscriptionManager.createSubscription error:', error);
      throw error;
    }
  }

  /**
   * Update existing subscription with new service combination
   * Requirements: 1.1, 1.2, 1.3, 3.1, 3.2, 3.3
   */
  async updateSubscription(subscriptionId: string, serviceIds: string[]): Promise<ModularSubscription> {
    try {
      // Validate input
      if (!subscriptionId) {
        throw new Error('Subscription ID is required');
      }
      if (!serviceIds || serviceIds.length === 0) {
        throw new Error('Service IDs are required');
      }

      // Validate service combination
      const validationResult = await this.serviceValidator.validateServiceCombination(serviceIds);
      if (!validationResult.isValid) {
        throw new Error(`Invalid service combination: ${validationResult.errors.join(', ')}`);
      }

      // Get existing subscription
      const { data: existingSubscription, error: fetchError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('id', subscriptionId)
        .single();

      if (fetchError) {
        throw new Error(`Subscription not found: ${fetchError.message}`);
      }

      if (existingSubscription.status !== 'active') {
        throw new Error('Cannot update inactive subscription');
      }

      // Calculate new pricing
      const pricingResult = await this.pricingEngine.calculateOptimalPrice(serviceIds);
      
      // Update subscription with new plan and pricing
      const { data: updatedSubscription, error: updateError } = await supabase
        .from('user_subscriptions')
        .update({
          plan_id: pricingResult.recommendedPlanId,
          total_price_cents: pricingResult.priceCents,
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update subscription: ${updateError.message}`);
      }

      // Requirement 3.3: Update billing to reflect the new service combination
      const updateServicesResult = await supabase.rpc('update_user_services', {
        user_uuid: existingSubscription.user_id,
        new_service_ids: serviceIds
      });

      if (updateServicesResult.error) {
        // Rollback subscription update
        await supabase
          .from('user_subscriptions')
          .update({
            plan_id: existingSubscription.plan_id,
            total_price_cents: existingSubscription.total_price_cents,
            updated_at: existingSubscription.updated_at
          })
          .eq('id', subscriptionId);
        
        throw new Error(`Failed to update user services: ${updateServicesResult.error.message}`);
      }

      return {
        id: updatedSubscription.id,
        userId: updatedSubscription.user_id,
        planId: updatedSubscription.plan_id,
        serviceIds: serviceIds,
        totalPriceCents: updatedSubscription.total_price_cents,
        status: updatedSubscription.status as 'active' | 'inactive' | 'cancelled' | 'past_due',
        stripeSubscriptionId: updatedSubscription.stripe_subscription_id,
        stripeCustomerId: updatedSubscription.stripe_customer_id,
        currentPeriodStart: new Date(updatedSubscription.current_period_start),
        currentPeriodEnd: new Date(updatedSubscription.current_period_end),
        createdAt: new Date(updatedSubscription.created_at),
        updatedAt: new Date(updatedSubscription.updated_at)
      };

    } catch (error) {
      console.error('SubscriptionManager.updateSubscription error:', error);
      throw error;
    }
  }

  /**
   * Get subscription details for a user
   * Requirements: 3.1, 3.4, 5.4
   */
  async getSubscriptionDetails(userId: string): Promise<SubscriptionDetails | null> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Use database function to get subscription details
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .rpc('get_user_subscription_details', {
          user_uuid: userId
        });

      if (subscriptionError) {
        throw new Error(`Failed to get subscription details: ${subscriptionError.message}`);
      }

      if (!subscriptionData || subscriptionData.length === 0) {
        return null; // No active subscription found
      }

      const subscription = subscriptionData[0];

      // Requirement 5.4: Return clear access status and expiration information
      return {
        subscriptionId: subscription.subscription_id,
        planId: subscription.plan_id,
        planName: subscription.plan_name,
        currentPriceCents: subscription.current_price_cents,
        status: subscription.status as 'active' | 'inactive' | 'cancelled' | 'past_due',
        services: subscription.services || [],
        storageQuotaGb: subscription.storage_quota_gb || 0,
        storageUsedGb: subscription.storage_used_gb || 0,
        nextBillingDate: new Date(subscription.next_billing_date)
      };

    } catch (error) {
      console.error('SubscriptionManager.getSubscriptionDetails error:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   * Requirements: 3.1, 3.4, 5.4
   */
  async cancelSubscription(subscriptionId: string): Promise<boolean> {
    try {
      // Validate input
      if (!subscriptionId) {
        throw new Error('Subscription ID is required');
      }

      // Get existing subscription to verify it exists and get user ID
      const { data: existingSubscription, error: fetchError } = await supabase
        .from('user_subscriptions')
        .select('id, user_id, status')
        .eq('id', subscriptionId)
        .single();

      if (fetchError) {
        throw new Error(`Subscription not found: ${fetchError.message}`);
      }

      if (existingSubscription.status === 'cancelled') {
        return true; // Already cancelled
      }

      // Requirement 3.4: Deactivate all associated services when subscription is cancelled
      const updateServicesResult = await supabase.rpc('update_user_services', {
        user_uuid: existingSubscription.user_id,
        new_service_ids: [] // Empty array deactivates all services
      });

      if (updateServicesResult.error) {
        throw new Error(`Failed to deactivate services: ${updateServicesResult.error.message}`);
      }

      // Update subscription status to cancelled
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionId);

      if (updateError) {
        // Rollback service deactivation if subscription update fails
        // Note: In a production system, this would be handled with database transactions
        console.error('Failed to update subscription status, but services were deactivated');
        throw new Error(`Failed to cancel subscription: ${updateError.message}`);
      }

      return true;

    } catch (error) {
      console.error('SubscriptionManager.cancelSubscription error:', error);
      throw error;
    }
  }

  /**
   * Check if user has access to a specific service
   */
  async checkServiceAccess(userId: string, serviceId: string): Promise<boolean> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }
      if (!serviceId) {
        throw new Error('Service ID is required');
      }

      // Use database function to check service access
      const { data, error } = await supabase.rpc('user_has_service_access', {
        p_user_id: userId,
        p_service_type_id: serviceId
      });

      if (error) {
        console.error('Error checking service access:', error);
        return false; // Default to no access on error
      }

      return data === true;
    } catch (error) {
      console.error('SubscriptionManager.checkServiceAccess error:', error);
      return false; // Default to no access on error
    }
  }

  /**
   * Get all services user has access to
   */
  async getUserServices(userId: string): Promise<string[]> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Get user's active service selections
      const { data, error } = await supabase
        .from('user_service_selections')
        .select('service_type_id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to get user services: ${error.message}`);
      }

      return data?.map(selection => selection.service_type_id) || [];
    } catch (error) {
      console.error('SubscriptionManager.getUserServices error:', error);
      throw error;
    }
  }

  /**
   * Update user's service selections
   */
  async updateUserServices(userId: string, serviceIds: string[]): Promise<boolean> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }
      if (!serviceIds || !Array.isArray(serviceIds)) {
        throw new Error('Service IDs array is required');
      }

      // Use database function to update user services
      const { data, error } = await supabase.rpc('update_user_services', {
        p_user_id: userId,
        p_service_ids: serviceIds
      });

      if (error) {
        throw new Error(`Failed to update user services: ${error.message}`);
      }

      return data?.success === true;
    } catch (error) {
      console.error('SubscriptionManager.updateUserServices error:', error);
      throw error;
    }
  }

  /**
   * Get user's storage quota based on selected services
   */
  async getUserStorageQuota(userId: string): Promise<number> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Use database function to calculate storage quota
      const { data, error } = await supabase.rpc('calculate_user_storage_quota', {
        p_user_id: userId
      });

      if (error) {
        throw new Error(`Failed to calculate storage quota: ${error.message}`);
      }

      return data || 1073741824; // Default to 1GB in bytes
    } catch (error) {
      console.error('SubscriptionManager.getUserStorageQuota error:', error);
      return 1073741824; // Default to 1GB in bytes on error
    }
  }

  /**
   * Validate subscription status and service access
   */
  async validateSubscriptionAccess(userId: string, requiredServices: string[]): Promise<{
    hasValidSubscription: boolean;
    hasServiceAccess: boolean;
    missingServices: string[];
    subscriptionStatus: string;
  }> {
    try {
      // Get user's subscription details
      const subscriptionDetails = await this.getSubscriptionDetails(userId);

      if (!subscriptionDetails) {
        return {
          hasValidSubscription: false,
          hasServiceAccess: false,
          missingServices: requiredServices,
          subscriptionStatus: 'none'
        };
      }

      // Check if subscription is active
      const hasValidSubscription = subscriptionDetails.status === 'active';

      if (!hasValidSubscription) {
        return {
          hasValidSubscription: false,
          hasServiceAccess: false,
          missingServices: requiredServices,
          subscriptionStatus: subscriptionDetails.status
        };
      }

      // Check service access
      const userServices = subscriptionDetails.services;
      const missingServices = requiredServices.filter(service => !userServices.includes(service));
      const hasServiceAccess = missingServices.length === 0;

      return {
        hasValidSubscription,
        hasServiceAccess,
        missingServices,
        subscriptionStatus: subscriptionDetails.status
      };
    } catch (error) {
      console.error('SubscriptionManager.validateSubscriptionAccess error:', error);
      return {
        hasValidSubscription: false,
        hasServiceAccess: false,
        missingServices: requiredServices,
        subscriptionStatus: 'error'
      };
    }
  }
}