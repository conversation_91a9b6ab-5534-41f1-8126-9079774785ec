-- SafeKeep Pricing Calculation Functions
-- Migration 007: Database functions for modular pricing logic
-- Run this script after 006_migrate_existing_users.sql

-- ============================================================================
-- FUNCTION 1: Calculate optimal pricing for service combinations
-- ============================================================================

CREATE OR REPLACE FUNCTION public.calculate_optimal_pricing(
    selected_services TEXT[]
)
RETURNS TABLE (
    service_combination TEXT[],
    individual_total_cents INTEGER,
    optimal_plan_id VARCHAR(50),
    optimal_plan_name VARCHAR(100),
    optimal_price_cents INTEGER,
    savings_cents INTEGER,
    savings_percentage DECIMAL(5,2),
    total_storage_gb INTEGER
) 
LANGUAGE plpgsql
AS $$
DECLARE
    individual_cost INTEGER := 0;
    optimal_cost INTEGER;
    optimal_plan RECORD;
    total_storage INTEGER := 0;
BEGIN
    -- Validate input services exist
    IF NOT EXISTS (
        SELECT 1 FROM public.service_types st 
        WHERE st.id = ANY(selected_services) AND st.is_active = TRUE
    ) THEN
        RAISE EXCEPTION 'Invalid service types provided';
    END IF;
    
    -- Calculate individual service cost and storage
    SELECT 
        COALESCE(SUM(st.base_price_cents), 0),
        COALESCE(SUM(st.storage_limit_gb), 0)
    INTO individual_cost, total_storage
    FROM public.service_types st
    WHERE st.id = ANY(selected_services) AND st.is_active = TRUE;
    
    -- Find optimal plan (exact match for services)
    SELECT sp.id, sp.name, sp.price_cents
    INTO optimal_plan
    FROM public.subscription_plans sp
    WHERE sp.is_active = TRUE
    AND (
        SELECT array_agg(ps.service_type_id ORDER BY ps.service_type_id)
        FROM public.plan_services ps 
        WHERE ps.plan_id = sp.id AND ps.is_included = TRUE
    ) = (
        SELECT array_agg(unnest ORDER BY unnest) 
        FROM unnest(selected_services)
    )
    ORDER BY sp.price_cents ASC
    LIMIT 1;
    
    -- Use individual pricing if no plan found
    optimal_cost := COALESCE(optimal_plan.price_cents, individual_cost);
    
    RETURN QUERY SELECT
        selected_services,
        individual_cost,
        COALESCE(optimal_plan.id, 'custom'),
        COALESCE(optimal_plan.name, 'Custom Selection'),
        optimal_cost,
        GREATEST(0, individual_cost - optimal_cost),
        CASE 
            WHEN individual_cost > 0 THEN 
                ROUND(((individual_cost - optimal_cost)::DECIMAL / individual_cost) * 100, 2)
            ELSE 0 
        END,
        total_storage;
END;
$$;

-- ============================================================================
-- FUNCTION 2: Update user service selections
-- ============================================================================

CREATE OR REPLACE FUNCTION public.update_user_services(
    p_user_id UUID,
    p_service_ids TEXT[]
)
RETURNS TABLE (
    success BOOLEAN,
    message TEXT,
    new_plan_id VARCHAR(50),
    new_total_cost_cents INTEGER,
    savings_cents INTEGER
)
LANGUAGE plpgsql
AS $$
DECLARE
    pricing_result RECORD;
    current_subscription RECORD;
BEGIN
    -- Validate user exists
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = p_user_id) THEN
        RETURN QUERY SELECT FALSE, 'User not found', NULL::VARCHAR(50), 0, 0;
        RETURN;
    END IF;
    
    -- Get optimal pricing for selected services
    SELECT * INTO pricing_result 
    FROM public.calculate_optimal_pricing(p_service_ids);
    
    -- Get current subscription
    SELECT * INTO current_subscription
    FROM public.user_subscriptions
    WHERE user_id = p_user_id;
    
    -- Begin transaction
    BEGIN
        -- Deactivate all current service selections
        UPDATE public.user_service_selections
        SET is_active = FALSE, deactivated_at = NOW(), updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- Insert/activate new service selections
        INSERT INTO public.user_service_selections (user_id, service_type_id, is_active, activated_at)
        SELECT p_user_id, unnest(p_service_ids), TRUE, NOW()
        ON CONFLICT (user_id, service_type_id) 
        DO UPDATE SET 
            is_active = TRUE, 
            activated_at = NOW(), 
            deactivated_at = NULL,
            updated_at = NOW();
        
        -- Update or create user subscription
        INSERT INTO public.user_subscriptions (
            user_id, plan_id, status, total_price_cents, service_combination,
            current_period_start, current_period_end
        )
        VALUES (
            p_user_id, 
            pricing_result.optimal_plan_id,
            'active',
            pricing_result.optimal_price_cents,
            to_jsonb(p_service_ids),
            NOW(),
            NOW() + INTERVAL '1 month'
        )
        ON CONFLICT (user_id) 
        DO UPDATE SET
            plan_id = EXCLUDED.plan_id,
            total_price_cents = EXCLUDED.total_price_cents,
            service_combination = EXCLUDED.service_combination,
            updated_at = NOW();
        
        -- Update users table
        UPDATE public.users
        SET 
            selected_services = to_jsonb(p_service_ids),
            subscription_plan_id = pricing_result.optimal_plan_id,
            total_subscription_cost_cents = pricing_result.optimal_price_cents
        WHERE id = p_user_id;
        
        RETURN QUERY SELECT 
            TRUE,
            'Services updated successfully',
            pricing_result.optimal_plan_id,
            pricing_result.optimal_price_cents,
            COALESCE(current_subscription.total_price_cents, 0) - pricing_result.optimal_price_cents;
            
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, 'Error updating services: ' || SQLERRM, NULL::VARCHAR(50), 0, 0;
    END;
END;
$$;

-- ============================================================================
-- FUNCTION 3: Get user service access permissions
-- ============================================================================

CREATE OR REPLACE FUNCTION public.user_has_service_access(
    p_user_id UUID,
    p_service_type_id VARCHAR(50)
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM public.user_service_selections uss
        WHERE uss.user_id = p_user_id 
        AND uss.service_type_id = p_service_type_id 
        AND uss.is_active = TRUE
    );
END;
$$;

-- ============================================================================
-- FUNCTION 4: Calculate user storage quota based on services
-- ============================================================================

CREATE OR REPLACE FUNCTION public.calculate_user_storage_quota(
    p_user_id UUID
)
RETURNS BIGINT
LANGUAGE plpgsql
AS $$
DECLARE
    total_quota_gb INTEGER := 0;
BEGIN
    SELECT COALESCE(SUM(st.storage_limit_gb), 0)
    INTO total_quota_gb
    FROM public.user_service_selections uss
    JOIN public.service_types st ON uss.service_type_id = st.id
    WHERE uss.user_id = p_user_id AND uss.is_active = TRUE;
    
    -- Convert GB to bytes (minimum 1GB)
    RETURN GREATEST(1073741824, total_quota_gb * 1073741824);
END;
$$;

-- ============================================================================
-- FUNCTION 5: Get service usage statistics
-- ============================================================================

CREATE OR REPLACE FUNCTION public.get_service_usage_stats(
    p_user_id UUID DEFAULT NULL
)
RETURNS TABLE (
    user_id UUID,
    service_type_id VARCHAR(50),
    service_name VARCHAR(100),
    is_active BOOLEAN,
    item_count INTEGER,
    size_bytes BIGINT,
    storage_limit_gb INTEGER,
    usage_percentage DECIMAL(5,2),
    last_sync TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        uss.user_id,
        uss.service_type_id,
        st.name as service_name,
        uss.is_active,
        CASE uss.service_type_id
            WHEN 'contacts' THEN COALESCE(su.contacts_count, 0)
            WHEN 'messages' THEN COALESCE(su.messages_count, 0)
            WHEN 'photos' THEN COALESCE(su.photos_count, 0)
            ELSE 0
        END as item_count,
        CASE uss.service_type_id
            WHEN 'contacts' THEN COALESCE(su.contacts_size_bytes, 0)
            WHEN 'messages' THEN COALESCE(su.messages_size_bytes, 0)
            WHEN 'photos' THEN COALESCE(su.photos_size_bytes, 0)
            ELSE 0
        END as size_bytes,
        st.storage_limit_gb,
        CASE 
            WHEN st.storage_limit_gb > 0 THEN
                ROUND((
                    CASE uss.service_type_id
                        WHEN 'contacts' THEN COALESCE(su.contacts_size_bytes, 0)
                        WHEN 'messages' THEN COALESCE(su.messages_size_bytes, 0)
                        WHEN 'photos' THEN COALESCE(su.photos_size_bytes, 0)
                        ELSE 0
                    END::DECIMAL / (st.storage_limit_gb * 1073741824)
                ) * 100, 2)
            ELSE 0
        END as usage_percentage,
        (su.last_service_sync ->> uss.service_type_id)::TIMESTAMP WITH TIME ZONE as last_sync
    FROM public.user_service_selections uss
    JOIN public.service_types st ON uss.service_type_id = st.id
    LEFT JOIN public.storage_usage su ON uss.user_id = su.user_id
    WHERE (p_user_id IS NULL OR uss.user_id = p_user_id)
    ORDER BY uss.user_id, st.sort_order;
END;
$$;

-- ============================================================================
-- FUNCTION 6: Recommend plan upgrades/downgrades
-- ============================================================================

CREATE OR REPLACE FUNCTION public.recommend_plan_changes(
    p_user_id UUID
)
RETURNS TABLE (
    current_plan_id VARCHAR(50),
    current_cost_cents INTEGER,
    recommended_plan_id VARCHAR(50),
    recommended_plan_name VARCHAR(100),
    recommended_cost_cents INTEGER,
    cost_difference_cents INTEGER,
    recommendation_reason TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    current_services TEXT[];
    current_sub RECORD;
    usage_stats RECORD;
BEGIN
    -- Get current subscription
    SELECT * INTO current_sub
    FROM public.user_subscriptions
    WHERE user_id = p_user_id;
    
    -- Get current active services
    SELECT array_agg(service_type_id ORDER BY service_type_id)
    INTO current_services
    FROM public.user_service_selections
    WHERE user_id = p_user_id AND is_active = TRUE;
    
    -- Get usage statistics
    SELECT 
        SUM(CASE WHEN service_type_id = 'contacts' THEN item_count ELSE 0 END) as contacts_count,
        SUM(CASE WHEN service_type_id = 'messages' THEN item_count ELSE 0 END) as messages_count,
        SUM(CASE WHEN service_type_id = 'photos' THEN item_count ELSE 0 END) as photos_count,
        SUM(size_bytes) as total_size_bytes
    INTO usage_stats
    FROM public.get_service_usage_stats(p_user_id);
    
    -- Return current vs optimal pricing
    RETURN QUERY
    SELECT 
        COALESCE(current_sub.plan_id, 'none'),
        COALESCE(current_sub.total_price_cents, 0),
        pricing.optimal_plan_id,
        pricing.optimal_plan_name,
        pricing.optimal_price_cents,
        pricing.optimal_price_cents - COALESCE(current_sub.total_price_cents, 0),
        CASE 
            WHEN pricing.optimal_price_cents < COALESCE(current_sub.total_price_cents, 0) THEN
                'You could save $' || ((COALESCE(current_sub.total_price_cents, 0) - pricing.optimal_price_cents)::DECIMAL / 100)::TEXT || ' per month'
            WHEN pricing.optimal_price_cents > COALESCE(current_sub.total_price_cents, 0) THEN
                'Upgrade recommended for better value and features'
            ELSE 'Your current plan is optimal'
        END
    FROM public.calculate_optimal_pricing(COALESCE(current_services, ARRAY[]::TEXT[])) pricing;
END;
$$;

-- ============================================================================
-- Add function comments and permissions
-- ============================================================================

COMMENT ON FUNCTION public.calculate_optimal_pricing(TEXT[]) IS 'Calculates the most cost-effective plan for given service combination';
COMMENT ON FUNCTION public.update_user_services(UUID, TEXT[]) IS 'Updates user service selections and subscription plan';
COMMENT ON FUNCTION public.user_has_service_access(UUID, VARCHAR(50)) IS 'Checks if user has access to specific service';
COMMENT ON FUNCTION public.calculate_user_storage_quota(UUID) IS 'Calculates total storage quota based on user services';
COMMENT ON FUNCTION public.get_service_usage_stats(UUID) IS 'Returns detailed usage statistics for user services';
COMMENT ON FUNCTION public.recommend_plan_changes(UUID) IS 'Recommends optimal plan changes based on usage patterns';

-- ============================================================================
-- Grant permissions for functions
-- ============================================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.calculate_optimal_pricing(TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_services(UUID, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_service_access(UUID, VARCHAR(50)) TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_user_storage_quota(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_service_usage_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.recommend_plan_changes(UUID) TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Pricing calculation functions created successfully!';
    RAISE NOTICE 'Available functions:';
    RAISE NOTICE '- calculate_optimal_pricing(services)';
    RAISE NOTICE '- update_user_services(user_id, services)';
    RAISE NOTICE '- user_has_service_access(user_id, service)';
    RAISE NOTICE '- calculate_user_storage_quota(user_id)';
    RAISE NOTICE '- get_service_usage_stats(user_id)';
    RAISE NOTICE '- recommend_plan_changes(user_id)';
END $$;
