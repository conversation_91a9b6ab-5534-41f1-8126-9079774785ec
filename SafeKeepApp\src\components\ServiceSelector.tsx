import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Checkbox,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';

export interface ServiceType {
  id: string;
  name: string;
  icon: string;
  price: number;
  storage: string;
  description: string;
  features: string[];
}

interface ServiceSelectorProps {
  service: ServiceType;
  isSelected: boolean;
  onToggle: (serviceId: string) => void;
  compact?: boolean;
}

const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  service,
  isSelected,
  onToggle,
  compact = false,
}) => {
  return (
    <TouchableOpacity
      onPress={() => onToggle(service.id)}
      style={[
        styles.serviceCard,
        isSelected && styles.selectedServiceCard,
        compact && styles.compactCard,
      ]}
    >
      <View style={styles.serviceHeader}>
        <View style={[styles.serviceIconContainer, isSelected && styles.selectedIconContainer]}>
          <Icon 
            name={service.icon} 
            size={compact ? 20 : 24} 
            color={isSelected ? COLORS.primary : COLORS.textSecondary} 
          />
        </View>
        
        <View style={styles.serviceInfo}>
          <Text style={[styles.serviceName, isSelected && styles.selectedServiceName]}>
            {service.name}
          </Text>
          {!compact && (
            <Text style={styles.serviceDescription}>
              {service.description}
            </Text>
          )}
        </View>
        
        <View style={styles.servicePricing}>
          <Text style={[styles.servicePrice, isSelected && styles.selectedServicePrice]}>
            ${(service.price / 100).toFixed(2)}
          </Text>
          <Text style={styles.servicePeriod}>/month</Text>
          <Checkbox
            status={isSelected ? 'checked' : 'unchecked'}
            onPress={() => onToggle(service.id)}
            color={COLORS.primary}
          />
        </View>
      </View>
      
      {!compact && (
        <View style={styles.serviceDetails}>
          <View style={styles.storageInfo}>
            <Icon name="cloud-outline" size={16} color={COLORS.textSecondary} />
            <Text style={styles.storageText}>{service.storage} storage</Text>
          </View>
          
          <View style={styles.featuresList}>
            {service.features.slice(0, 3).map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Icon name="check" size={14} color={COLORS.success} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
            {service.features.length > 3 && (
              <Text style={styles.moreFeatures}>
                +{service.features.length - 3} more features
              </Text>
            )}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  serviceCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    borderWidth: 2,
    borderColor: COLORS.border,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  compactCard: {
    padding: SPACING.md,
    marginBottom: SPACING.sm,
  },
  selectedServiceCard: {
    borderColor: COLORS.primary,
    backgroundColor: 'rgba(74, 144, 226, 0.05)',
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  selectedIconContainer: {
    backgroundColor: 'rgba(74, 144, 226, 0.1)',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  selectedServiceName: {
    color: COLORS.primary,
  },
  serviceDescription: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  servicePricing: {
    alignItems: 'flex-end',
  },
  servicePrice: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  selectedServicePrice: {
    color: COLORS.primary,
  },
  servicePeriod: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  serviceDetails: {
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    marginTop: SPACING.md,
  },
  storageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  storageText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  featuresList: {
    gap: SPACING.xs,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
    flex: 1,
  },
  moreFeatures: {
    fontSize: FONTS.sizes.small,
    color: COLORS.primary,
    fontStyle: 'italic',
    marginTop: SPACING.xs,
  },
});

export default ServiceSelector;
