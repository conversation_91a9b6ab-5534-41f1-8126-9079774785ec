# SafeKeep Modular Pricing - Stripe Integration Setup Guide

## 🚀 Quick Setup Commands

### 1. **Create Stripe Products and Prices**

Run the product creation script to set up all 7 pricing combinations:

```bash
# Set your Stripe secret key
export STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Run the product creation script
cd SafeKeepApp/stripe-setup
node create-modular-products.js
```

This will create:
- **3 Individual Services**: Contacts ($0.99), Messages ($1.99), Photos ($4.99)
- **4 Combination Plans**: Contacts+Messages ($2.49), Contacts+Photos ($5.49), Messages+Photos ($6.49), Complete Backup ($6.99)

### 2. **Manual Stripe CLI Commands** (Alternative)

If you prefer to create products manually:

```bash
# Install Stripe CLI
npm install -g stripe-cli

# Login to Stripe
stripe login

# Create Individual Services
stripe products create \
  --name "Contacts Backup" \
  --description "Secure backup and sync of your contacts" \
  --metadata[plan_id]=contacts-only \
  --metadata[services]=contacts \
  --metadata[storage]=1GB

stripe prices create \
  --product prod_contacts_id_here \
  --unit-amount 99 \
  --currency usd \
  --recurring[interval]=month \
  --nickname "Contacts Only - Monthly"

# Repeat for Messages and Photos...

# Create Combination Plans
stripe products create \
  --name "Complete Backup" \
  --description "All services included - best value!" \
  --metadata[plan_id]=complete-backup \
  --metadata[services]="contacts,messages,photos" \
  --metadata[storage]=13GB \
  --metadata[savings]=98 \
  --metadata[is_popular]=true

stripe prices create \
  --product prod_complete_backup_id_here \
  --unit-amount 699 \
  --currency usd \
  --recurring[interval]=month \
  --nickname "Complete Backup - Monthly"
```

## 🔧 Environment Configuration

Add these environment variables to your `.env` files:

### **Backend API (.env)**
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (from product creation script output)
STRIPE_PRICE_CONTACTS_ONLY=price_contacts_only_id
STRIPE_PRICE_MESSAGES_ONLY=price_messages_only_id
STRIPE_PRICE_PHOTOS_ONLY=price_photos_only_id
STRIPE_PRICE_CONTACTS_MESSAGES=price_contacts_messages_id
STRIPE_PRICE_CONTACTS_PHOTOS=price_contacts_photos_id
STRIPE_PRICE_MESSAGES_PHOTOS=price_messages_photos_id
STRIPE_PRICE_COMPLETE_BACKUP=price_complete_backup_id

# API Configuration
API_BASE_URL=http://localhost:3000/api
```

### **React Native App (.env)**
```bash
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# API Configuration
API_BASE_URL=http://localhost:3000/api
```

## 📡 Webhook Configuration

### **1. Set up Webhook Endpoint in Stripe Dashboard**

1. Go to [Stripe Dashboard > Webhooks](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Enter your endpoint URL: `https://your-api-domain.com/api/billing/webhook`
4. Select these events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.created`
   - `customer.updated`

### **2. Test Webhooks Locally**

```bash
# Forward webhooks to local development server
stripe listen --forward-to localhost:3000/api/billing/webhook

# Test webhook with sample event
stripe trigger customer.subscription.created
```

## 🔄 Updated Payment Flow

### **Frontend Integration**

```typescript
import { modularStripeService } from './services/ModularStripeService';

// 1. Calculate optimal pricing
const pricingResult = modularStripeService.calculateOptimalPricing(['contacts', 'messages']);

// 2. Create subscription
const subscriptionResponse = await fetch('/api/billing/subscriptions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userToken}`,
  },
  body: JSON.stringify({
    userId: user.id,
    serviceIds: ['contacts', 'messages'],
    planId: 'contacts-messages',
    priceCents: 249,
    billingEmail: user.email,
  }),
});

// 3. Confirm payment if needed
if (subscriptionData.client_secret) {
  const { error, paymentIntent } = await confirmPayment(
    subscriptionData.client_secret,
    { paymentMethodType: 'Card' }
  );
}
```

### **Backend API Routes**

```typescript
// Create payment intent
POST /api/billing/payment-intent
{
  "amount": 699,
  "currency": "usd",
  "serviceIds": ["contacts", "messages", "photos"],
  "userId": "user-uuid",
  "planId": "complete-backup"
}

// Create subscription
POST /api/billing/subscriptions
{
  "userId": "user-uuid",
  "serviceIds": ["contacts", "messages"],
  "planId": "contacts-messages",
  "priceCents": 249
}

// Update subscription
PUT /api/billing/subscriptions/:subscriptionId
{
  "serviceIds": ["contacts", "messages", "photos"],
  "planId": "complete-backup",
  "priceCents": 699
}
```

## 💳 Billing Descriptors

Each plan has a custom billing descriptor that appears on customer statements:

- **Contacts Only**: `SafeKeep Contacts`
- **Messages Only**: `SafeKeep Messages`
- **Photos Only**: `SafeKeep Photos`
- **Contacts + Messages**: `SafeKeep Contacts+Messages`
- **Contacts + Photos**: `SafeKeep Contacts+Photos`
- **Messages + Photos**: `SafeKeep Messages+Photos`
- **Complete Backup**: `SafeKeep Complete`

## 🔄 Subscription Management

### **Adding/Removing Services**

```typescript
// User wants to add Photos to existing Contacts+Messages subscription
const updateResponse = await fetch(`/api/billing/subscriptions/${subscriptionId}`, {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userToken}`,
  },
  body: JSON.stringify({
    serviceIds: ['contacts', 'messages', 'photos'], // New service combination
    planId: 'complete-backup', // New optimal plan
    priceCents: 699, // New price
    prorationBehavior: 'create_prorations' // Handle prorations
  }),
});
```

### **Proration Handling**

- **`create_prorations`**: Create prorations for mid-cycle changes (default)
- **`none`**: No prorations, changes take effect next billing cycle
- **`always_invoice`**: Always create an invoice for the proration

## 🧪 Testing

### **Test Cards for Different Scenarios**

```bash
# Successful payment
****************

# Requires authentication (3D Secure)
****************

# Declined card
****************

# Insufficient funds
****************
```

### **Test Webhook Events**

```bash
# Test subscription creation
stripe trigger customer.subscription.created

# Test payment success
stripe trigger invoice.payment_succeeded

# Test payment failure
stripe trigger invoice.payment_failed
```

## 📊 Monitoring & Analytics

### **Key Metrics to Track**

1. **Conversion Rates**:
   - Service selection patterns
   - Plan upgrade/downgrade rates
   - Payment completion rates

2. **Revenue Metrics**:
   - Average revenue per user (ARPU)
   - Monthly recurring revenue (MRR)
   - Customer lifetime value (CLV)

3. **Service Popularity**:
   - Most selected individual services
   - Most popular combinations
   - Savings utilization rates

### **Stripe Dashboard Monitoring**

- Monitor subscription growth in Stripe Dashboard
- Set up alerts for failed payments
- Track churn and retention metrics
- Analyze revenue trends by plan type

## 🚨 Error Handling

### **Common Error Scenarios**

1. **Invalid Service Combination**: Return clear error with valid options
2. **Payment Method Declined**: Provide retry mechanism with different card
3. **Webhook Processing Failure**: Implement retry logic with exponential backoff
4. **Proration Calculation Error**: Fall back to next billing cycle changes

### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "PAYMENT_FAILED",
    "message": "Your card was declined. Please try a different payment method.",
    "details": {
      "decline_code": "insufficient_funds",
      "suggested_action": "try_different_card"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

This comprehensive Stripe integration provides a robust foundation for the modular pricing system with proper error handling, webhook processing, and subscription management capabilities.
