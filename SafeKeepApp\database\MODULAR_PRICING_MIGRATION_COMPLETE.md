# SafeKeep Modular Pricing Migration Guide

## Overview

This comprehensive migration updates your SafeKeeping database to support the new modular pricing structure with individual service selections, combination packages, and backward compatibility with existing subscriptions.

## 🎯 What This Migration Accomplishes

### ✅ **New Modular Pricing Structure**
- **Individual Services**: Contacts ($0.99), Messages ($1.99), Photos ($4.99)
- **Combination Packages**: 2-service and 3-service bundles with savings
- **Complete Backup**: All services for $6.99 (best value)

### ✅ **Enhanced Database Schema**
- Service types table with pricing and storage limits
- Flexible subscription plans with combination support
- User service selections tracking
- Comprehensive usage analytics
- Backward compatibility with existing tiers

### ✅ **Advanced Features**
- Automatic pricing optimization
- Storage quota calculation based on services
- Usage tracking per service type
- Plan recommendation engine
- Stripe integration ready

## 📋 Migration Scripts (Run in Order)

### 1. **004_modular_pricing_schema.sql**
**Purpose**: Creates the foundation database schema
- Creates service_types, subscription_plans, plan_services tables
- Adds user_service_selections and user_subscriptions tables
- Updates existing tables with new columns
- Creates indexes for performance

### 2. **005_populate_modular_pricing_data.sql**
**Purpose**: Populates the new tables with pricing data
- Inserts 3 service types (contacts, messages, photos)
- Creates 7 subscription plans (3 individual + 4 combinations)
- Links services to plans via junction table
- Creates legacy tier mapping for backward compatibility

### 3. **006_migrate_existing_users.sql**
**Purpose**: Migrates existing users to new structure
- Backs up existing subscription data
- Migrates users based on their current subscription_tier
- Creates service selections for migrated users
- Updates storage usage with service-specific tracking

### 4. **007_pricing_calculation_functions.sql**
**Purpose**: Adds business logic functions
- `calculate_optimal_pricing()` - finds best plan for service combination
- `update_user_services()` - safely updates user service selections
- `user_has_service_access()` - checks service permissions
- `calculate_user_storage_quota()` - computes storage limits
- `get_service_usage_stats()` - detailed usage analytics
- `recommend_plan_changes()` - suggests optimal plans

### 5. **008_modular_pricing_rls_policies.sql**
**Purpose**: Implements security and access controls
- Row Level Security on all new tables
- User-specific access to subscriptions and selections
- Public read access to pricing data
- Automatic storage quota updates via triggers

### 6. **009_verify_modular_pricing.sql**
**Purpose**: Verifies migration success
- Tests all functions and data integrity
- Performance benchmarks
- Security policy validation
- Sample pricing scenarios

## 🚀 Step-by-Step Migration Process

### Prerequisites
```bash
# 1. Backup your database
pg_dump your_database > backup_before_modular_pricing.sql

# 2. Test in development environment first
# 3. Ensure you have admin access to Supabase
```

### Step 1: Run Foundation Schema
```sql
-- Copy and paste 004_modular_pricing_schema.sql into Supabase SQL Editor
-- This creates all new tables and indexes
```

### Step 2: Populate Pricing Data
```sql
-- Copy and paste 005_populate_modular_pricing_data.sql into Supabase SQL Editor
-- This adds all service types and subscription plans
```

### Step 3: Migrate Existing Users
```sql
-- Copy and paste 006_migrate_existing_users.sql into Supabase SQL Editor
-- This preserves existing user subscriptions with backward compatibility
```

### Step 4: Add Business Logic
```sql
-- Copy and paste 007_pricing_calculation_functions.sql into Supabase SQL Editor
-- This adds all pricing calculation and management functions
```

### Step 5: Implement Security
```sql
-- Copy and paste 008_modular_pricing_rls_policies.sql into Supabase SQL Editor
-- This secures all data with proper access controls
```

### Step 6: Verify Migration
```sql
-- Copy and paste 009_verify_modular_pricing.sql into Supabase SQL Editor
-- This validates everything is working correctly
```

## 📊 New Database Schema

### Core Tables

#### `service_types`
```sql
id                  VARCHAR(50)    -- 'contacts', 'messages', 'photos'
name                VARCHAR(100)   -- Display name
base_price_cents    INTEGER        -- Price in cents
storage_limit_gb    INTEGER        -- Storage allocation
```

#### `subscription_plans`
```sql
id                  VARCHAR(50)    -- Plan identifier
name                VARCHAR(100)   -- Display name
price_cents         INTEGER        -- Total price in cents
is_combination      BOOLEAN        -- Multi-service plan
savings_cents       INTEGER        -- Savings vs individual
```

#### `user_service_selections`
```sql
user_id             UUID           -- User reference
service_type_id     VARCHAR(50)    -- Selected service
is_active           BOOLEAN        -- Current status
activated_at        TIMESTAMP      -- When activated
```

#### `user_subscriptions`
```sql
user_id             UUID           -- User reference
plan_id             VARCHAR(50)    -- Current plan
total_price_cents   INTEGER        -- Total cost
service_combination JSONB          -- Selected services array
stripe_subscription_id VARCHAR(100) -- Stripe integration
```

## 💰 Pricing Structure

### Individual Services
- **Contacts Only**: $0.99/month (1GB storage)
- **Messages Only**: $1.99/month (2GB storage)
- **Photos Only**: $4.99/month (10GB storage)

### Combination Packages
- **Contacts + Messages**: $2.49/month (3GB) - Save $0.49
- **Contacts + Photos**: $5.49/month (11GB) - Save $0.49
- **Messages + Photos**: $6.49/month (12GB) - Save $0.49
- **Complete Backup**: $6.99/month (13GB) - Save $0.98

## 🔧 Key Functions Available

### Pricing Calculations
```sql
-- Get optimal pricing for service combination
SELECT * FROM calculate_optimal_pricing(ARRAY['contacts', 'photos']);

-- Update user services
SELECT * FROM update_user_services(user_id, ARRAY['contacts', 'messages']);

-- Check service access
SELECT user_has_service_access(user_id, 'photos');
```

### Usage Analytics
```sql
-- Get detailed usage stats
SELECT * FROM get_service_usage_stats(user_id);

-- Get plan recommendations
SELECT * FROM recommend_plan_changes(user_id);

-- Calculate storage quota
SELECT calculate_user_storage_quota(user_id);
```

## 🔒 Security Features

- **Row Level Security** on all tables
- **User-specific access** to subscriptions and selections
- **Public read access** to pricing data only
- **Automatic quota updates** when services change
- **Audit trail** for all subscription changes

## 🔄 Backward Compatibility

### Legacy Tier Mapping
- `basic` → `contacts-only` (maintains service continuity)
- `premium` → `complete-backup` (preserves full access)
- `family` → `complete-backup` (shared storage maintained)

### Migration Safety
- Original subscription data backed up in `user_subscription_backup`
- Legacy tier preserved in `legacy_tier_id` column
- Gradual migration path available
- Rollback procedures documented

## ✅ Post-Migration Checklist

1. **Verify Data Integrity**
   - Run verification script
   - Check user migration success
   - Validate pricing calculations

2. **Update Application Code**
   - Update subscription management
   - Implement service selection UI
   - Add usage tracking

3. **Test Stripe Integration**
   - Verify webhook handling
   - Test plan changes
   - Validate billing cycles

4. **Monitor Performance**
   - Check query performance
   - Monitor storage usage
   - Track user adoption

## 🆘 Troubleshooting

### Common Issues

**Migration fails on existing users**
```sql
-- Check for data conflicts
SELECT * FROM user_subscription_backup WHERE subscription_tier IS NULL;
```

**Pricing calculations incorrect**
```sql
-- Verify service types and plans
SELECT * FROM service_combination_pricing;
```

**RLS policies blocking access**
```sql
-- Check user authentication
SELECT auth.uid(), auth.role();
```

## 📞 Support

If you encounter issues during migration:
1. Check the verification script output
2. Review error messages in Supabase logs
3. Ensure all scripts ran in correct order
4. Verify user permissions and RLS policies

The migration is designed to be safe and reversible. All original data is preserved for rollback if needed.
