import React from 'react';
import {
  View,
  StyleSheet,
  Modal,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { COLORS, SPACING, FONTS } from '../utils/constants';

interface UpgradePromptProps {
  visible: boolean;
  serviceId: 'contacts' | 'messages' | 'photos';
  reason?: 'no_subscription' | 'service_not_included' | 'subscription_expired' | 'subscription_cancelled';
  onUpgrade: () => void;
  onCancel: () => void;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  visible,
  serviceId,
  reason = 'no_subscription',
  onUpgrade,
  onCancel,
}) => {
  const serviceInfo = {
    contacts: {
      name: 'Contacts Backup',
      icon: 'account-multiple',
      description: 'Secure backup and sync of your contacts',
      features: [
        'Contact deduplication',
        'Cross-device sync',
        'Backup history',
        'Export options'
      ],
      price: '$0.99/month'
    },
    messages: {
      name: 'Messages Backup',
      icon: 'message-text',
      description: 'Secure backup and sync of your messages',
      features: [
        'SMS & MMS backup',
        'Message threading',
        'Search functionality',
        'Media attachments'
      ],
      price: '$1.99/month'
    },
    photos: {
      name: 'Photos Backup',
      icon: 'camera',
      description: 'Secure backup and sync of your photos',
      features: [
        'Automatic photo backup',
        'Smart compression',
        'Album organization',
        'Face recognition'
      ],
      price: '$4.99/month'
    }
  };

  const service = serviceInfo[serviceId];

  const getReasonMessage = () => {
    switch (reason) {
      case 'no_subscription':
        return 'You need a SafeKeep subscription to access this feature.';
      case 'service_not_included':
        return 'This service is not included in your current plan.';
      case 'subscription_expired':
        return 'Your subscription has expired. Renew to continue using this feature.';
      case 'subscription_cancelled':
        return 'Your subscription was cancelled. Reactivate to access this feature.';
      default:
        return 'Premium subscription required for this feature.';
    }
  };

  const getActionText = () => {
    switch (reason) {
      case 'subscription_expired':
      case 'subscription_cancelled':
        return 'Reactivate Plan';
      case 'service_not_included':
        return 'Upgrade Plan';
      default:
        return 'Choose Plan';
    }
  };

  const getSavingsMessage = () => {
    if (serviceId === 'contacts') {
      return 'Save $0.49/month with Contacts + Messages bundle';
    } else if (serviceId === 'messages') {
      return 'Save $0.49/month with Messages + Photos bundle';
    } else if (serviceId === 'photos') {
      return 'Save $0.98/month with Complete Backup bundle';
    }
    return null;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Card style={styles.card}>
            <Card.Content style={styles.cardContent}>
              {/* Header */}
              <View style={styles.header}>
                <View style={styles.serviceIconContainer}>
                  <Icon name={service.icon} size={40} color={COLORS.primary} />
                </View>
                <View style={styles.headerText}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <Text style={styles.serviceDescription}>{service.description}</Text>
                </View>
                <Chip style={styles.premiumChip} textStyle={styles.premiumChipText} icon="crown">
                  Premium
                </Chip>
              </View>

              {/* Reason */}
              <View style={styles.reasonContainer}>
                <Icon name="lock" size={20} color={COLORS.warning} />
                <Text style={styles.reasonText}>{getReasonMessage()}</Text>
              </View>

              {/* Features */}
              <View style={styles.featuresContainer}>
                <Text style={styles.featuresTitle}>What you'll get:</Text>
                {service.features.map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Icon name="check-circle" size={16} color={COLORS.success} />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
              </View>

              {/* Pricing */}
              <View style={styles.pricingContainer}>
                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Starting at</Text>
                  <Text style={styles.price}>{service.price}</Text>
                </View>
                {getSavingsMessage() && (
                  <View style={styles.savingsContainer}>
                    <Icon name="trending-down" size={16} color={COLORS.success} />
                    <Text style={styles.savingsText}>{getSavingsMessage()}</Text>
                  </View>
                )}
              </View>

              {/* Security Notice */}
              <View style={styles.securityNotice}>
                <Icon name="shield-check" size={16} color={COLORS.success} />
                <Text style={styles.securityText}>
                  Military-grade AES-256 encryption • Zero-knowledge architecture
                </Text>
              </View>

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={onCancel}
                  style={styles.cancelButton}
                >
                  Not Now
                </Button>
                <Button
                  mode="contained"
                  onPress={onUpgrade}
                  style={styles.upgradeButton}
                  contentStyle={styles.upgradeButtonContent}
                >
                  {getActionText()}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  container: {
    width: '100%',
    maxWidth: 400,
  },
  card: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  cardContent: {
    padding: SPACING.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  serviceIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(74, 144, 226, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  headerText: {
    flex: 1,
  },
  serviceName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  serviceDescription: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  premiumChip: {
    backgroundColor: COLORS.primary,
    alignSelf: 'flex-start',
  },
  premiumChipText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: FONTS.sizes.small,
  },
  reasonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.lg,
  },
  reasonText: {
    flex: 1,
    fontSize: FONTS.sizes.medium,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    lineHeight: 20,
  },
  featuresContainer: {
    marginBottom: SPACING.lg,
  },
  featuresTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  pricingContainer: {
    marginBottom: SPACING.lg,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  priceLabel: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
  },
  price: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: SPACING.sm,
    borderRadius: 6,
  },
  savingsText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.success,
    marginLeft: SPACING.xs,
    fontWeight: '500',
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: SPACING.sm,
    borderRadius: 6,
    marginBottom: SPACING.lg,
  },
  securityText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.success,
    marginLeft: SPACING.sm,
    fontWeight: '500',
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  upgradeButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
  },
  upgradeButtonContent: {
    paddingVertical: SPACING.xs,
  },
});

export default UpgradePrompt;
