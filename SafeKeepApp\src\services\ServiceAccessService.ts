import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Service access interface
export interface ServiceAccess {
  contacts: boolean;
  messages: boolean;
  photos: boolean;
}

export interface UserSubscription {
  id: string;
  planId: string;
  planName: string;
  serviceIds: string[];
  status: 'active' | 'cancelled' | 'past_due' | 'incomplete';
  currentPeriodEnd: string;
  priceCents: number;
}

export interface AccessCheckResult {
  hasAccess: boolean;
  reason?: 'no_subscription' | 'service_not_included' | 'subscription_expired' | 'subscription_cancelled';
  upgradeRequired?: boolean;
  suggestedPlan?: string;
}

class ServiceAccessService {
  private static instance: ServiceAccessService;
  private cachedAccess: ServiceAccess | null = null;
  private cachedSubscription: UserSubscription | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';

  static getInstance(): ServiceAccessService {
    if (!ServiceAccessService.instance) {
      ServiceAccessService.instance = new ServiceAccessService();
    }
    return ServiceAccessService.instance;
  }

  /**
   * Check if user has access to a specific service
   */
  async hasServiceAccess(serviceId: 'contacts' | 'messages' | 'photos'): Promise<AccessCheckResult> {
    try {
      const subscription = await this.getUserSubscription();
      
      if (!subscription) {
        return {
          hasAccess: false,
          reason: 'no_subscription',
          upgradeRequired: true,
          suggestedPlan: this.getSuggestedPlan([serviceId])
        };
      }

      if (subscription.status !== 'active') {
        return {
          hasAccess: false,
          reason: subscription.status === 'cancelled' ? 'subscription_cancelled' : 'subscription_expired',
          upgradeRequired: true
        };
      }

      const hasAccess = subscription.serviceIds.includes(serviceId);
      
      if (!hasAccess) {
        return {
          hasAccess: false,
          reason: 'service_not_included',
          upgradeRequired: true,
          suggestedPlan: this.getSuggestedPlan([...subscription.serviceIds, serviceId])
        };
      }

      return { hasAccess: true };
    } catch (error) {
      console.error('Error checking service access:', error);
      return {
        hasAccess: false,
        reason: 'no_subscription',
        upgradeRequired: true
      };
    }
  }

  /**
   * Get all service access permissions
   */
  async getAllServiceAccess(): Promise<ServiceAccess> {
    // Check cache first
    if (this.cachedAccess && Date.now() < this.cacheExpiry) {
      return this.cachedAccess;
    }

    try {
      const subscription = await this.getUserSubscription();
      
      const access: ServiceAccess = {
        contacts: subscription?.serviceIds.includes('contacts') && subscription.status === 'active' || false,
        messages: subscription?.serviceIds.includes('messages') && subscription.status === 'active' || false,
        photos: subscription?.serviceIds.includes('photos') && subscription.status === 'active' || false,
      };

      // Cache the result
      this.cachedAccess = access;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;

      return access;
    } catch (error) {
      console.error('Error getting service access:', error);
      return {
        contacts: false,
        messages: false,
        photos: false,
      };
    }
  }

  /**
   * Get user's current subscription details
   */
  async getUserSubscription(): Promise<UserSubscription | null> {
    // Check cache first
    if (this.cachedSubscription && Date.now() < this.cacheExpiry) {
      return this.cachedSubscription;
    }

    try {
      const userToken = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');

      if (!userToken || !userId) {
        return null;
      }

      const response = await fetch(`${this.API_BASE_URL}/subscriptions/${userId}`, {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No subscription found
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data) {
        const subscription: UserSubscription = {
          id: data.data.subscriptionId,
          planId: data.data.planId,
          planName: data.data.planName,
          serviceIds: data.data.services,
          status: data.data.status,
          currentPeriodEnd: data.data.nextBillingDate,
          priceCents: data.data.currentPriceCents,
        };

        // Cache the result
        this.cachedSubscription = subscription;
        this.cacheExpiry = Date.now() + this.CACHE_DURATION;

        return subscription;
      }

      return null;
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return null;
    }
  }

  /**
   * Show upgrade prompt for service access
   */
  async showUpgradePrompt(
    serviceId: 'contacts' | 'messages' | 'photos',
    onUpgrade?: () => void,
    onCancel?: () => void
  ): Promise<void> {
    const serviceNames = {
      contacts: 'Contacts Backup',
      messages: 'Messages Backup',
      photos: 'Photos Backup'
    };

    const serviceName = serviceNames[serviceId];
    const subscription = await this.getUserSubscription();
    
    let message = `${serviceName} is not included in your current plan.`;
    let upgradeText = 'Upgrade Plan';

    if (!subscription) {
      message = `${serviceName} requires a SafeKeep subscription.`;
      upgradeText = 'Choose Plan';
    } else if (subscription.status !== 'active') {
      message = `Your subscription is ${subscription.status}. Reactivate to access ${serviceName}.`;
      upgradeText = 'Reactivate';
    }

    Alert.alert(
      '🔒 Premium Feature',
      `${message}\n\nUpgrade to unlock secure backup for your ${serviceId} with military-grade encryption.`,
      [
        {
          text: 'Not Now',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: upgradeText,
          onPress: onUpgrade,
        },
      ]
    );
  }

  /**
   * Get suggested plan for service combination
   */
  private getSuggestedPlan(serviceIds: string[]): string {
    const sortedServices = serviceIds.sort().join(',');
    
    const planMapping: { [key: string]: string } = {
      'contacts': 'contacts-only',
      'messages': 'messages-only',
      'photos': 'photos-only',
      'contacts,messages': 'contacts-messages',
      'contacts,photos': 'contacts-photos',
      'messages,photos': 'messages-photos',
      'contacts,messages,photos': 'complete-backup',
    };

    return planMapping[sortedServices] || 'complete-backup';
  }

  /**
   * Clear cached access data (call when subscription changes)
   */
  clearCache(): void {
    this.cachedAccess = null;
    this.cachedSubscription = null;
    this.cacheExpiry = 0;
  }

  /**
   * Refresh access data from server
   */
  async refreshAccess(): Promise<ServiceAccess> {
    this.clearCache();
    return await this.getAllServiceAccess();
  }

  /**
   * Check if user has any active subscription
   */
  async hasActiveSubscription(): Promise<boolean> {
    const subscription = await this.getUserSubscription();
    return subscription?.status === 'active' || false;
  }

  /**
   * Get subscription status for display
   */
  async getSubscriptionStatus(): Promise<{
    hasSubscription: boolean;
    status: string;
    planName: string;
    serviceCount: number;
    nextBilling?: string;
  }> {
    const subscription = await this.getUserSubscription();
    
    if (!subscription) {
      return {
        hasSubscription: false,
        status: 'none',
        planName: 'No Plan',
        serviceCount: 0,
      };
    }

    return {
      hasSubscription: true,
      status: subscription.status,
      planName: subscription.planName,
      serviceCount: subscription.serviceIds.length,
      nextBilling: subscription.currentPeriodEnd,
    };
  }

  /**
   * Validate access before performing service operations
   */
  async validateServiceOperation(
    serviceId: 'contacts' | 'messages' | 'photos',
    operation: string,
    showPrompt: boolean = true
  ): Promise<boolean> {
    const accessResult = await this.hasServiceAccess(serviceId);
    
    if (!accessResult.hasAccess) {
      console.log(`Access denied for ${serviceId} ${operation}: ${accessResult.reason}`);
      
      if (showPrompt) {
        await this.showUpgradePrompt(serviceId);
      }
      
      return false;
    }

    return true;
  }

  /**
   * Get feature availability matrix
   */
  async getFeatureAvailability(): Promise<{
    [feature: string]: {
      available: boolean;
      requiresUpgrade: boolean;
      requiredService: string;
    };
  }> {
    const access = await this.getAllServiceAccess();
    
    return {
      contactsBackup: {
        available: access.contacts,
        requiresUpgrade: !access.contacts,
        requiredService: 'contacts'
      },
      contactsSync: {
        available: access.contacts,
        requiresUpgrade: !access.contacts,
        requiredService: 'contacts'
      },
      messagesBackup: {
        available: access.messages,
        requiresUpgrade: !access.messages,
        requiredService: 'messages'
      },
      messagesSearch: {
        available: access.messages,
        requiresUpgrade: !access.messages,
        requiredService: 'messages'
      },
      photosBackup: {
        available: access.photos,
        requiresUpgrade: !access.photos,
        requiredService: 'photos'
      },
      photosOrganization: {
        available: access.photos,
        requiresUpgrade: !access.photos,
        requiredService: 'photos'
      },
      autoBackup: {
        available: access.contacts || access.messages || access.photos,
        requiresUpgrade: !access.contacts && !access.messages && !access.photos,
        requiredService: 'any'
      }
    };
  }
}

// Export singleton instance
export const serviceAccessService = ServiceAccessService.getInstance();

// Export convenience functions
export const hasContactsAccess = () => serviceAccessService.hasServiceAccess('contacts');
export const hasMessagesAccess = () => serviceAccessService.hasServiceAccess('messages');
export const hasPhotosAccess = () => serviceAccessService.hasServiceAccess('photos');

export default serviceAccessService;
