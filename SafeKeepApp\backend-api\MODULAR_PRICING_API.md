# SafeKeep Modular Pricing API Documentation

## Overview

This API supports the new modular pricing system where users can select individual services (contacts, messages, photos) or combination packages with automatic savings calculations.

## Base URL
```
https://your-api-domain.com/api
```

## Authentication
All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 🎯 Pricing Calculation Endpoints

### Calculate Optimal Pricing
Calculate the best pricing plan for a given service combination.

**POST** `/pricing/calculate`

**Request Body:**
```json
{
  "serviceIds": ["contacts", "messages", "photos"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendedPlanId": "complete-backup",
    "recommendedPlanName": "Complete Backup",
    "priceCents": 699,
    "savingsCents": 98,
    "individualTotalCents": 797,
    "totalStorageGb": 13,
    "savingsPercentage": 12.3
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Bulk Pricing Calculation
Calculate pricing for multiple service combinations at once.

**GET** `/pricing/bulk?serviceCombinations=[["contacts"],["messages"],["contacts","photos"]]`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "recommendedPlanId": "contacts-only",
      "recommendedPlanName": "Contacts Only",
      "priceCents": 99,
      "savingsCents": 0,
      "individualTotalCents": 99,
      "totalStorageGb": 1,
      "savingsPercentage": 0
    },
    {
      "recommendedPlanId": "messages-only",
      "recommendedPlanName": "Messages Only", 
      "priceCents": 199,
      "savingsCents": 0,
      "individualTotalCents": 199,
      "totalStorageGb": 2,
      "savingsPercentage": 0
    },
    {
      "recommendedPlanId": "contacts-photos",
      "recommendedPlanName": "Contacts + Photos",
      "priceCents": 549,
      "savingsCents": 49,
      "individualTotalCents": 598,
      "totalStorageGb": 11,
      "savingsPercentage": 8.2
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get Individual Service Pricing
Get base pricing for all individual services.

**GET** `/pricing/services`

**Response:**
```json
{
  "success": true,
  "data": {
    "contacts": 99,
    "messages": 199,
    "photos": 499
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 📋 Subscription Management Endpoints

### Create Subscription
Create a new subscription with selected services.

**POST** `/subscriptions`

**Request Body:**
```json
{
  "userId": "user-uuid-here",
  "serviceIds": ["contacts", "messages"],
  "paymentMethodId": "pm_stripe_payment_method_id"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "subscription-uuid",
    "userId": "user-uuid-here",
    "planId": "contacts-messages",
    "serviceIds": ["contacts", "messages"],
    "totalPriceCents": 249,
    "status": "active",
    "stripeSubscriptionId": "sub_stripe_id",
    "stripeCustomerId": "cus_stripe_id",
    "currentPeriodStart": "2024-01-15T00:00:00Z",
    "currentPeriodEnd": "2024-02-15T00:00:00Z",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Update Subscription
Update an existing subscription's services.

**PUT** `/subscriptions/{subscriptionId}`

**Request Body:**
```json
{
  "serviceIds": ["contacts", "messages", "photos"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "subscription-uuid",
    "userId": "user-uuid-here",
    "planId": "complete-backup",
    "serviceIds": ["contacts", "messages", "photos"],
    "totalPriceCents": 699,
    "status": "active",
    "currentPeriodStart": "2024-01-15T00:00:00Z",
    "currentPeriodEnd": "2024-02-15T00:00:00Z",
    "updatedAt": "2024-01-15T10:35:00Z"
  }
}
```

### Get Subscription Details
Get detailed subscription information for a user.

**GET** `/subscriptions/{userId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "subscription-uuid",
    "planId": "complete-backup",
    "planName": "Complete Backup",
    "currentPriceCents": 699,
    "status": "active",
    "services": ["contacts", "messages", "photos"],
    "storageQuotaGb": 13,
    "storageUsedGb": 2.5,
    "nextBillingDate": "2024-02-15T00:00:00Z"
  }
}
```

### Cancel Subscription
Cancel an active subscription.

**DELETE** `/subscriptions/{subscriptionId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "cancelled": true
  }
}
```

## 🔐 Service Access Validation Endpoints

### Check Service Access
Check if a user has access to a specific service.

**GET** `/subscriptions/{userId}/access/{serviceId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "hasAccess": true,
    "serviceId": "photos"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get User Services
Get all services a user has access to.

**GET** `/subscriptions/{userId}/services`

**Response:**
```json
{
  "success": true,
  "data": {
    "services": ["contacts", "messages", "photos"]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🚨 Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "additionalInfo": "Optional additional details"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Common Error Codes

- `INVALID_INPUT` - Invalid or missing request parameters
- `INVALID_SERVICE_COMBINATION` - Invalid service combination provided
- `SUBSCRIPTION_NOT_FOUND` - Subscription not found for user
- `SUBSCRIPTION_EXISTS` - User already has an active subscription
- `PRICING_CALCULATION_ERROR` - Error calculating pricing
- `SERVICE_ACCESS_CHECK_ERROR` - Error checking service access
- `AUTHENTICATION_ERROR` - Invalid or missing authentication token

## 📊 Service Types

Available service types and their individual pricing:

| Service ID | Name | Price (cents) | Storage (GB) |
|------------|------|---------------|--------------|
| `contacts` | Contacts Backup | 99 | 1 |
| `messages` | Messages Backup | 199 | 2 |
| `photos` | Photos Backup | 499 | 10 |

## 💰 Subscription Plans

Available subscription plans with automatic savings:

| Plan ID | Name | Services | Price (cents) | Savings |
|---------|------|----------|---------------|---------|
| `contacts-only` | Contacts Only | contacts | 99 | - |
| `messages-only` | Messages Only | messages | 199 | - |
| `photos-only` | Photos Only | photos | 499 | - |
| `contacts-messages` | Contacts + Messages | contacts, messages | 249 | 49¢ |
| `contacts-photos` | Contacts + Photos | contacts, photos | 549 | 49¢ |
| `messages-photos` | Messages + Photos | messages, photos | 649 | 49¢ |
| `complete-backup` | Complete Backup | contacts, messages, photos | 699 | 98¢ |

## 🔧 Integration Examples

### React Native Integration
```typescript
// Calculate pricing for service selection
const calculatePricing = async (serviceIds: string[]) => {
  const response = await fetch('/api/pricing/calculate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ serviceIds })
  });
  
  const result = await response.json();
  return result.data;
};

// Create subscription
const createSubscription = async (userId: string, serviceIds: string[]) => {
  const response = await fetch('/api/subscriptions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ userId, serviceIds })
  });
  
  return response.json();
};

// Check service access before allowing feature use
const checkAccess = async (userId: string, serviceId: string) => {
  const response = await fetch(`/api/subscriptions/${userId}/access/${serviceId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  const result = await response.json();
  return result.data.hasAccess;
};
```

This API provides comprehensive support for the modular pricing system with automatic optimization, service validation, and flexible subscription management.
