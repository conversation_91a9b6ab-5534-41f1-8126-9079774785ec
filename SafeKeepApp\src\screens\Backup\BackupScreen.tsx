import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { SegmentedButtons, Text, ActivityIndicator } from 'react-native-paper';
import { Header } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// Removed unused import
import { COLORS, FONTS, SPACING } from '../../utils/constants';
import PhotoBackupScreen from './PhotoBackupScreen';
import ContactBackupScreen from './ContactBackupScreen';
import MessageBackupScreen from './MessageBackupScreen';
import { BackupDashboard, BackupProgressScreen } from '../../components/backup';
import { useIsBackupInProgress } from '../../store/hooks/backupHooks';
import { RootStackParamList } from '../../navigation/AppNavigator';
import ServiceAccessGuard from '../../components/ServiceAccessGuard';
import { serviceAccessService, ServiceAccess } from '../../services/ServiceAccessService';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const BackupScreen = () => {
  const [selectedTab, setSelectedTab] = useState('dashboard');
  const [showProgressScreen, setShowProgressScreen] = useState(false);
  const [serviceAccess, setServiceAccess] = useState<ServiceAccess | null>(null);
  const [isLoadingAccess, setIsLoadingAccess] = useState(true);
  const navigation = useNavigation<NavigationProp>();
  const isBackupInProgress = useIsBackupInProgress();

  useEffect(() => {
    loadServiceAccess();
  }, []);

  const loadServiceAccess = async () => {
    try {
      const access = await serviceAccessService.getAllServiceAccess();
      setServiceAccess(access);
    } catch (error) {
      console.error('Error loading service access:', error);
      setServiceAccess({ contacts: false, messages: false, photos: false });
    } finally {
      setIsLoadingAccess(false);
    }
  };

  const handleNavigateToProgress = () => {
    setShowProgressScreen(true);
  };

  const handleNavigateBack = () => {
    setShowProgressScreen(false);
  };
  
  const handleNavigateToHistory = () => {
    navigation.navigate('BackupHistory');
  };
  
  const handleNavigateToSettings = () => {
    navigation.navigate('BackupSettings', {});
  };

  const renderTabContent = () => {
    if (showProgressScreen) {
      return <BackupProgressScreen onNavigateBack={handleNavigateBack} />;
    }

    switch (selectedTab) {
      case 'dashboard':
        return <BackupDashboard
          onNavigateToProgress={handleNavigateToProgress}
          onNavigateToSettings={handleNavigateToSettings}
          onNavigateToHistory={handleNavigateToHistory}
          serviceAccess={serviceAccess}
        />;
      case 'photos':
        return (
          <ServiceAccessGuard serviceId="photos">
            <PhotoBackupScreen />
          </ServiceAccessGuard>
        );
      case 'contacts':
        return (
          <ServiceAccessGuard serviceId="contacts">
            <ContactBackupScreen />
          </ServiceAccessGuard>
        );
      case 'messages':
        return (
          <ServiceAccessGuard serviceId="messages">
            <MessageBackupScreen />
          </ServiceAccessGuard>
        );
      default:
        return <BackupDashboard
          onNavigateToProgress={handleNavigateToProgress}
          onNavigateToSettings={handleNavigateToSettings}
          onNavigateToHistory={handleNavigateToHistory}
          serviceAccess={serviceAccess}
        />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Backup Center',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <View style={styles.tabContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'dashboard',
              label: 'Dashboard',
              icon: 'view-dashboard',
            },
            {
              value: 'photos',
              label: serviceAccess?.photos ? 'Photos' : 'Photos 🔒',
              icon: 'camera',
              disabled: isLoadingAccess,
            },
            {
              value: 'contacts',
              label: serviceAccess?.contacts ? 'Contacts' : 'Contacts 🔒',
              icon: 'contacts',
              disabled: isLoadingAccess,
            },
            {
              value: 'messages',
              label: serviceAccess?.messages ? 'Messages' : 'Messages 🔒',
              icon: 'message-text',
              disabled: isLoadingAccess,
            },
          ]}
          style={styles.segmentedButtons}
        />

        {isLoadingAccess && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={COLORS.primary} />
            <Text style={styles.loadingText}>Checking access...</Text>
          </View>
        )}
      </View>

      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  tabContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.surface,
    elevation: 2,
  },
  segmentedButtons: {
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    flex: 1,
  },
  comingSoon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  comingSoonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  comingSoonSubtext: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
  },
  loadingText: {
    marginLeft: SPACING.sm,
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
  },
});

export default BackupScreen;
