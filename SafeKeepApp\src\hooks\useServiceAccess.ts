import { useState, useEffect, useCallback } from 'react';
import { serviceAccessService, ServiceAccess, UserSubscription, AccessCheckResult } from '../services/ServiceAccessService';

export interface UseServiceAccessReturn {
  serviceAccess: ServiceAccess | null;
  subscription: UserSubscription | null;
  isLoading: boolean;
  error: string | null;
  hasContactsAccess: boolean;
  hasMessagesAccess: boolean;
  hasPhotosAccess: boolean;
  hasAnyAccess: boolean;
  refreshAccess: () => Promise<void>;
  checkServiceAccess: (serviceId: 'contacts' | 'messages' | 'photos') => Promise<AccessCheckResult>;
  validateOperation: (serviceId: 'contacts' | 'messages' | 'photos', operation: string, showPrompt?: boolean) => Promise<boolean>;
}

/**
 * Hook for managing service access state and operations
 */
export const useServiceAccess = (): UseServiceAccessReturn => {
  const [serviceAccess, setServiceAccess] = useState<ServiceAccess | null>(null);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [accessData, subscriptionData] = await Promise.all([
        serviceAccessService.getAllServiceAccess(),
        serviceAccessService.getUserSubscription(),
      ]);

      setServiceAccess(accessData);
      setSubscription(subscriptionData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load service access';
      setError(errorMessage);
      console.error('Error loading service access:', err);
      
      // Set default values on error
      setServiceAccess({ contacts: false, messages: false, photos: false });
      setSubscription(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const refreshAccess = useCallback(async () => {
    await loadData();
  }, [loadData]);

  const checkServiceAccess = useCallback(async (serviceId: 'contacts' | 'messages' | 'photos'): Promise<AccessCheckResult> => {
    return await serviceAccessService.hasServiceAccess(serviceId);
  }, []);

  const validateOperation = useCallback(async (
    serviceId: 'contacts' | 'messages' | 'photos',
    operation: string,
    showPrompt: boolean = true
  ): Promise<boolean> => {
    return await serviceAccessService.validateServiceOperation(serviceId, operation, showPrompt);
  }, []);

  // Computed values
  const hasContactsAccess = serviceAccess?.contacts || false;
  const hasMessagesAccess = serviceAccess?.messages || false;
  const hasPhotosAccess = serviceAccess?.photos || false;
  const hasAnyAccess = hasContactsAccess || hasMessagesAccess || hasPhotosAccess;

  return {
    serviceAccess,
    subscription,
    isLoading,
    error,
    hasContactsAccess,
    hasMessagesAccess,
    hasPhotosAccess,
    hasAnyAccess,
    refreshAccess,
    checkServiceAccess,
    validateOperation,
  };
};

/**
 * Hook for checking access to a specific service
 */
export const useSpecificServiceAccess = (serviceId: 'contacts' | 'messages' | 'photos') => {
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [accessResult, setAccessResult] = useState<AccessCheckResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        setIsLoading(true);
        const result = await serviceAccessService.hasServiceAccess(serviceId);
        setAccessResult(result);
        setHasAccess(result.hasAccess);
      } catch (error) {
        console.error(`Error checking ${serviceId} access:`, error);
        setHasAccess(false);
        setAccessResult({
          hasAccess: false,
          reason: 'no_subscription',
          upgradeRequired: true
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, [serviceId]);

  const showUpgradePrompt = useCallback(() => {
    serviceAccessService.showUpgradePrompt(serviceId);
  }, [serviceId]);

  const validateOperation = useCallback(async (operation: string, showPrompt: boolean = true): Promise<boolean> => {
    return await serviceAccessService.validateServiceOperation(serviceId, operation, showPrompt);
  }, [serviceId]);

  return {
    hasAccess,
    accessResult,
    isLoading,
    showUpgradePrompt,
    validateOperation,
  };
};

/**
 * Hook for subscription status information
 */
export const useSubscriptionStatus = () => {
  const [status, setStatus] = useState<{
    hasSubscription: boolean;
    status: string;
    planName: string;
    serviceCount: number;
    nextBilling?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStatus = async () => {
      try {
        setIsLoading(true);
        const statusData = await serviceAccessService.getSubscriptionStatus();
        setStatus(statusData);
      } catch (error) {
        console.error('Error loading subscription status:', error);
        setStatus({
          hasSubscription: false,
          status: 'none',
          planName: 'No Plan',
          serviceCount: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadStatus();
  }, []);

  const refreshStatus = useCallback(async () => {
    const statusData = await serviceAccessService.getSubscriptionStatus();
    setStatus(statusData);
  }, []);

  return {
    status,
    isLoading,
    refreshStatus,
  };
};

/**
 * Hook for feature availability matrix
 */
export const useFeatureAvailability = () => {
  const [features, setFeatures] = useState<{
    [feature: string]: {
      available: boolean;
      requiresUpgrade: boolean;
      requiredService: string;
    };
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadFeatures = async () => {
      try {
        setIsLoading(true);
        const featureData = await serviceAccessService.getFeatureAvailability();
        setFeatures(featureData);
      } catch (error) {
        console.error('Error loading feature availability:', error);
        setFeatures({});
      } finally {
        setIsLoading(false);
      }
    };

    loadFeatures();
  }, []);

  const isFeatureAvailable = useCallback((featureName: string): boolean => {
    return features?.[featureName]?.available || false;
  }, [features]);

  const getFeatureRequirement = useCallback((featureName: string): string | null => {
    return features?.[featureName]?.requiredService || null;
  }, [features]);

  return {
    features,
    isLoading,
    isFeatureAvailable,
    getFeatureRequirement,
  };
};

export default useServiceAccess;
