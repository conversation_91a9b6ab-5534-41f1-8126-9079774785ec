import Stripe from 'stripe';
import { BillingMetadata, PaymentResult, WebhookResult, SubscriptionRequest } from '../types/modular-pricing';

export class BillingIntegration {
  private stripe: Stripe;

  constructor() {
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    
    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2023-08-16',
    });
  }

  /**
   * Create payment intent with modular pricing metadata
   */
  async createPaymentIntent(amount: number, metadata: BillingMetadata): Promise<PaymentResult> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        metadata: {
          userId: metadata.userId,
          serviceIds: JSON.stringify(metadata.serviceIds),
          planId: metadata.planId,
          planName: metadata.planName,
          type: 'modular_subscription'
        },
        description: `SafeKeep ${metadata.planName} subscription for services: ${metadata.serviceIds.join(', ')}`,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        success: true,
        paymentIntentId: paymentIntent.id,
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment intent',
      };
    }
  }

  /**
   * Process subscription payment for service combinations
   */
  async processSubscriptionPayment(subscriptionData: SubscriptionRequest): Promise<PaymentResult> {
    try {
      let customerId = subscriptionData.customerId;

      // Create customer if not provided
      if (!customerId) {
        const customer = await this.stripe.customers.create({
          metadata: {
            userId: subscriptionData.userId,
            serviceIds: JSON.stringify(subscriptionData.serviceIds),
          },
        });
        customerId = customer.id;
      }

      // Attach payment method to customer if provided
      if (subscriptionData.paymentMethodId) {
        await this.stripe.paymentMethods.attach(subscriptionData.paymentMethodId, {
          customer: customerId,
        });

        // Set as default payment method
        await this.stripe.customers.update(customerId, {
          invoice_settings: {
            default_payment_method: subscriptionData.paymentMethodId,
          },
        });
      }

      // Create subscription with service metadata and proper pricing
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: 'SafeKeep Modular Backup Services',
                metadata: {
                  services: subscriptionData.serviceIds.join(','),
                  plan_id: (subscriptionData as any).planId || 'custom'
                }
              },
              unit_amount: (subscriptionData as any).priceCents || 0,
              recurring: {
                interval: 'month',
              },
            },
          },
        ],
        metadata: {
          userId: subscriptionData.userId,
          serviceIds: JSON.stringify(subscriptionData.serviceIds),
          planId: (subscriptionData as any).planId || 'custom',
          type: 'modular_subscription',
        },
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent'],
      });

      const invoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

      return {
        success: true,
        subscriptionId: subscription.id,
        paymentIntentId: paymentIntent?.id,
      };
    } catch (error) {
      console.error('Error processing subscription payment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process subscription payment',
      };
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhook(event: Stripe.Event): Promise<WebhookResult> {
    try {
      console.log(`Processing webhook event: ${event.type}`);

      switch (event.type) {
        case 'invoice.payment_succeeded':
          return await this.handleInvoicePaymentSucceeded(event);
        
        case 'invoice.payment_failed':
          return await this.handleInvoicePaymentFailed(event);
        
        case 'customer.subscription.created':
          return await this.handleSubscriptionCreated(event);
        
        case 'customer.subscription.updated':
          return await this.handleSubscriptionUpdated(event);
        
        case 'customer.subscription.deleted':
          return await this.handleSubscriptionDeleted(event);
        
        case 'payment_intent.succeeded':
          return await this.handlePaymentIntentSucceeded(event);
        
        case 'payment_intent.payment_failed':
          return await this.handlePaymentIntentFailed(event);
        
        default:
          console.log(`Unhandled webhook event type: ${event.type}`);
          return {
            processed: true,
            action: 'ignored',
          };
      }
    } catch (error) {
      console.error('Error processing webhook:', error);
      return {
        processed: false,
        error: error instanceof Error ? error.message : 'Failed to process webhook',
      };
    }
  }

  /**
   * Handle successful invoice payment
   */
  private async handleInvoicePaymentSucceeded(event: Stripe.Event): Promise<WebhookResult> {
    const invoice = event.data.object as Stripe.Invoice;
    const subscriptionId = invoice.subscription as string;

    if (subscriptionId) {
      // Update subscription status to active
      console.log(`Invoice payment succeeded for subscription: ${subscriptionId}`);
      
      // Here you would typically update your database to mark the subscription as active
      // This would integrate with your SubscriptionManager or database layer
      
      return {
        processed: true,
        action: 'subscription_activated',
      };
    }

    return {
      processed: true,
      action: 'invoice_payment_processed',
    };
  }

  /**
   * Handle failed invoice payment
   */
  private async handleInvoicePaymentFailed(event: Stripe.Event): Promise<WebhookResult> {
    const invoice = event.data.object as Stripe.Invoice;
    const subscriptionId = invoice.subscription as string;

    if (subscriptionId) {
      console.log(`Invoice payment failed for subscription: ${subscriptionId}`);
      
      // Here you would typically update your database to mark the subscription as past_due
      // and potentially send notification to user
      
      return {
        processed: true,
        action: 'subscription_payment_failed',
      };
    }

    return {
      processed: true,
      action: 'invoice_payment_failed',
    };
  }

  /**
   * Handle subscription creation
   */
  private async handleSubscriptionCreated(event: Stripe.Event): Promise<WebhookResult> {
    const subscription = event.data.object as Stripe.Subscription;
    
    console.log(`Subscription created: ${subscription.id}`);
    
    // Extract service information from metadata
    const userId = subscription.metadata?.userId;
    const serviceIds = subscription.metadata?.serviceIds;
    
    if (userId && serviceIds) {
      // Here you would typically create or update the subscription in your database
      console.log(`Creating subscription for user ${userId} with services: ${serviceIds}`);
      
      return {
        processed: true,
        action: 'subscription_created',
      };
    }

    return {
      processed: true,
      action: 'subscription_created_no_metadata',
    };
  }

  /**
   * Handle subscription updates
   */
  private async handleSubscriptionUpdated(event: Stripe.Event): Promise<WebhookResult> {
    const subscription = event.data.object as Stripe.Subscription;
    
    console.log(`Subscription updated: ${subscription.id}, status: ${subscription.status}`);
    
    // Extract service information from metadata
    const userId = subscription.metadata?.userId;
    const serviceIds = subscription.metadata?.serviceIds;
    
    if (userId) {
      // Here you would typically update the subscription in your database
      console.log(`Updating subscription for user ${userId}, new status: ${subscription.status}`);
      
      // Handle different subscription statuses
      switch (subscription.status) {
        case 'active':
          return {
            processed: true,
            action: 'subscription_activated',
          };
        case 'past_due':
          return {
            processed: true,
            action: 'subscription_past_due',
          };
        case 'canceled':
          return {
            processed: true,
            action: 'subscription_canceled',
          };
        case 'unpaid':
          return {
            processed: true,
            action: 'subscription_unpaid',
          };
        default:
          return {
            processed: true,
            action: 'subscription_status_updated',
          };
      }
    }

    return {
      processed: true,
      action: 'subscription_updated_no_metadata',
    };
  }

  /**
   * Handle subscription deletion
   */
  private async handleSubscriptionDeleted(event: Stripe.Event): Promise<WebhookResult> {
    const subscription = event.data.object as Stripe.Subscription;
    
    console.log(`Subscription deleted: ${subscription.id}`);
    
    const userId = subscription.metadata?.userId;
    
    if (userId) {
      // Here you would typically deactivate the subscription and services in your database
      console.log(`Deactivating subscription for user ${userId}`);
      
      return {
        processed: true,
        action: 'subscription_deleted',
      };
    }

    return {
      processed: true,
      action: 'subscription_deleted_no_metadata',
    };
  }

  /**
   * Handle successful payment intent
   */
  private async handlePaymentIntentSucceeded(event: Stripe.Event): Promise<WebhookResult> {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    
    console.log(`Payment intent succeeded: ${paymentIntent.id}`);
    
    // Check if this is for a modular subscription
    if (paymentIntent.metadata?.type === 'modular_subscription') {
      const userId = paymentIntent.metadata?.userId;
      const serviceIds = paymentIntent.metadata?.serviceIds;
      
      if (userId && serviceIds) {
        console.log(`Payment succeeded for user ${userId} services: ${serviceIds}`);
        
        return {
          processed: true,
          action: 'modular_payment_succeeded',
        };
      }
    }

    return {
      processed: true,
      action: 'payment_intent_succeeded',
    };
  }

  /**
   * Handle failed payment intent
   */
  private async handlePaymentIntentFailed(event: Stripe.Event): Promise<WebhookResult> {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    
    console.log(`Payment intent failed: ${paymentIntent.id}`);
    
    // Check if this is for a modular subscription
    if (paymentIntent.metadata?.type === 'modular_subscription') {
      const userId = paymentIntent.metadata?.userId;
      const serviceIds = paymentIntent.metadata?.serviceIds;
      
      if (userId && serviceIds) {
        console.log(`Payment failed for user ${userId} services: ${serviceIds}`);
        
        return {
          processed: true,
          action: 'modular_payment_failed',
        };
      }
    }

    return {
      processed: true,
      action: 'payment_intent_failed',
    };
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload: string, signature: string, endpointSecret: string): Stripe.Event {
    try {
      return this.stripe.webhooks.constructEvent(payload, signature, endpointSecret);
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      throw new Error('Invalid webhook signature');
    }
  }

  /**
   * Update subscription with new service combination
   */
  async updateSubscription(subscriptionId: string, serviceIds: string[], newAmount: number): Promise<PaymentResult> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      
      // Update subscription metadata
      await this.stripe.subscriptions.update(subscriptionId, {
        metadata: {
          ...subscription.metadata,
          serviceIds: JSON.stringify(serviceIds),
          updatedAt: new Date().toISOString(),
        },
      });

      // Update the subscription item with new pricing
      if (subscription.items.data.length > 0) {
        // Create a new price for the updated service combination
        const price = await this.stripe.prices.create({
          currency: 'usd',
          unit_amount: newAmount,
          recurring: {
            interval: 'month',
          },
          product_data: {
            name: 'SafeKeep Modular Backup Services',
            metadata: {
              services: serviceIds.join(','),
            },
          },
        });

        await this.stripe.subscriptionItems.update(subscription.items.data[0].id, {
          price: price.id,
        });
      }

      return {
        success: true,
        subscriptionId,
      };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update subscription',
      };
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<PaymentResult> {
    try {
      await this.stripe.subscriptions.cancel(subscriptionId);
      
      return {
        success: true,
        subscriptionId,
      };
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel subscription',
      };
    }
  }

  /**
   * Retrieve customer by user ID
   */
  async getCustomerByUserId(userId: string): Promise<Stripe.Customer | null> {
    try {
      const customers = await this.stripe.customers.list({
        limit: 1,
      });

      // Find customer with matching userId in metadata
      for (const customer of customers.data) {
        if (customer.metadata?.userId === userId) {
          return customer;
        }
      }

      return null;
    } catch (error) {
      console.error('Error retrieving customer:', error);
      return null;
    }
  }
}