import Stripe from 'stripe';
import { StripeProvider, useStripe, useConfirmPayment, initStripe } from '@stripe/stripe-react-native';

// IMPORTANT: In React Native, environment variables work differently
// For production, use a secure config management solution
const STRIPE_SECRET_KEY = 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP'; // This should be on your backend only
const STRIPE_PUBLISHABLE_KEY = 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP';

// Note: In production, the secret key should NEVER be in the mobile app
// It should only be on your secure backend server
console.log('⚠️ Using hardcoded Stripe keys for development. In production, use secure config management.');

// Note: Server-side Stripe operations should be handled by your backend
// This service focuses on React Native client-side operations

// Payment Intent interface for SafeKeep subscriptions
export interface PaymentIntentData {
  amount: number; // Amount in cents (e.g., 999 for $9.99)
  currency: string; // e.g., 'usd'
  customerId?: string;
  description?: string;
  metadata?: Record<string, string>;
}

// Modular subscription plans for SafeKeep
export const SAFEKEEP_PLANS = {
  // Individual Services
  CONTACTS_ONLY: {
    id: 'contacts_only',
    name: 'Contacts Only',
    price: 99, // $0.99/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 1,
    features: [
      '1GB Secure Storage',
      'Contact Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['contacts']
  },
  MESSAGES_ONLY: {
    id: 'messages_only',
    name: 'Messages Only',
    price: 199, // $1.99/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 1,
    features: [
      '1GB Secure Storage',
      'Message Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['messages']
  },
  PHOTOS_ONLY: {
    id: 'photos_only',
    name: 'Photos Only',
    price: 499, // $4.99/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 10,
    features: [
      '10GB Secure Storage',
      'Photo & Video Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['photos']
  },
  // Combination Services
  CONTACTS_MESSAGES: {
    id: 'contacts_messages',
    name: 'Contacts + Messages',
    price: 249, // $2.49/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 2,
    features: [
      '2GB Secure Storage',
      'Contact Backup',
      'Message Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['contacts', 'messages']
  },
  CONTACTS_PHOTOS: {
    id: 'contacts_photos',
    name: 'Contacts + Photos',
    price: 549, // $5.49/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 11,
    features: [
      '11GB Secure Storage',
      'Contact Backup',
      'Photo & Video Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['contacts', 'photos']
  },
  MESSAGES_PHOTOS: {
    id: 'messages_photos',
    name: 'Messages + Photos',
    price: 649, // $6.49/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 11,
    features: [
      '11GB Secure Storage',
      'Message Backup',
      'Photo & Video Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
    services: ['messages', 'photos']
  },
  COMPLETE_BACKUP: {
    id: 'complete_backup',
    name: 'Complete Backup',
    price: 699, // $6.99/month in cents
    currency: 'usd',
    interval: 'month',
    storageGB: 12,
    features: [
      '12GB Secure Storage',
      'Contact Backup',
      'Message Backup',
      'Photo & Video Backup',
      'Priority Support',
      'End-to-End Encryption'
    ],
    services: ['contacts', 'messages', 'photos']
  },
} as const;

// React Native Stripe Service - Client-side operations only
export const StripeService = {
  // Get publishable key for frontend use
  getPublishableKey: (): string => {
    return STRIPE_PUBLISHABLE_KEY;
  },

  // Get plan by ID
  getPlan: (planId: string) => {
    return Object.values(SAFEKEEP_PLANS).find(plan => plan.id === planId);
  },

  // Get all plans
  getAllPlans: () => {
    return SAFEKEEP_PLANS;
  },

  // Create payment intent via backend API
  createPaymentIntent: async (data: {
    amount: number;
    currency: string;
    description: string;
    planId?: string;
  }) => {
    try {
      console.log('🔄 Creating payment intent via backend...', data);

      const response = await fetch('http://localhost:3000/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const paymentIntent = await response.json();
      console.log('✅ Payment intent created:', paymentIntent.payment_intent_id);
      return paymentIntent;
    } catch (error: unknown) {
      console.error('❌ Failed to create payment intent:', error);
      // Re-throw with proper error handling
      if (error instanceof Error) {
        throw error; // Preserve original Error object if it's already an Error
      } else {
        throw new Error(`Failed to create payment intent: ${String(error)}`);
      }
    }
  },
};

// React Native Stripe initialization
export const initializeStripe = async () => {
  try {
    await initStripe({
      publishableKey: STRIPE_PUBLISHABLE_KEY,
      merchantIdentifier: 'merchant.com.safekeep.app', // For Apple Pay
      urlScheme: 'safekeep', // For redirects
    });
    console.log('✅ Stripe initialized successfully for React Native');
    return true;
  } catch (error: unknown) {
    console.error('❌ Failed to initialize Stripe:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Stripe initialization failed: ${errorMessage}`);
  }
};

// Export React Native Stripe components for frontend use
export { StripeProvider, useStripe, useConfirmPayment, initStripe };

// Export publishable key for frontend initialization
export const STRIPE_PUBLISHABLE_KEY_EXPORT = STRIPE_PUBLISHABLE_KEY;

// Export default service
export default StripeService;
