-- SafeKeep Modular Pricing Data Population
-- Migration 005: Populate service types and subscription plans
-- Run this script after 004_modular_pricing_schema.sql

-- ============================================================================
-- STEP 1: Insert service types (individual backup services)
-- ============================================================================

INSERT INTO public.service_types (id, name, description, base_price_cents, storage_limit_gb, sort_order) VALUES
('contacts', 'Contacts Backup', 'Secure backup and sync of your contacts with deduplication', 99, 1, 1),
('messages', 'Messages Backup', 'Secure backup and sync of your messages and SMS with threading', 199, 2, 2),
('photos', 'Photos Backup', 'Secure backup and sync of your photos and media with compression', 499, 10, 3)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    base_price_cents = EXCLUDED.base_price_cents,
    storage_limit_gb = EXCLUDED.storage_limit_gb,
    sort_order = EXCLUDED.sort_order,
    updated_at = NOW();

-- ============================================================================
-- STEP 2: Insert subscription plans
-- ============================================================================

-- Individual service plans
INSERT INTO public.subscription_plans (
    id, name, description, price_cents, total_storage_gb, 
    is_combination, is_popular, discount_percentage, savings_cents, sort_order
) VALUES
-- Individual Services
('contacts-only', 'Contacts Only', 'Backup and sync your contacts securely', 99, 1, FALSE, FALSE, 0, 0, 1),
('messages-only', 'Messages Only', 'Backup and sync your messages and SMS', 199, 2, FALSE, FALSE, 0, 0, 2),
('photos-only', 'Photos Only', 'Backup and sync your photos and media', 499, 10, FALSE, FALSE, 0, 0, 3),

-- Two-service combinations
('contacts-messages', 'Contacts + Messages', 'Essential communication backup bundle', 249, 3, TRUE, FALSE, 17, 49, 4),
('contacts-photos', 'Contacts + Photos', 'Personal memories and connections bundle', 549, 11, TRUE, FALSE, 8, 49, 5),
('messages-photos', 'Messages + Photos', 'Complete personal data bundle', 649, 12, TRUE, TRUE, 7, 49, 6),

-- Complete package
('complete-backup', 'Complete Backup', 'All services included - best value!', 699, 13, TRUE, FALSE, 17, 98, 7)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price_cents = EXCLUDED.price_cents,
    total_storage_gb = EXCLUDED.total_storage_gb,
    is_combination = EXCLUDED.is_combination,
    is_popular = EXCLUDED.is_popular,
    discount_percentage = EXCLUDED.discount_percentage,
    savings_cents = EXCLUDED.savings_cents,
    sort_order = EXCLUDED.sort_order,
    updated_at = NOW();

-- ============================================================================
-- STEP 3: Link services to plans (plan_services junction table)
-- ============================================================================

-- Individual service plans
INSERT INTO public.plan_services (plan_id, service_type_id, is_included) VALUES
-- Contacts Only
('contacts-only', 'contacts', TRUE),

-- Messages Only  
('messages-only', 'messages', TRUE),

-- Photos Only
('photos-only', 'photos', TRUE),

-- Contacts + Messages
('contacts-messages', 'contacts', TRUE),
('contacts-messages', 'messages', TRUE),

-- Contacts + Photos
('contacts-photos', 'contacts', TRUE),
('contacts-photos', 'photos', TRUE),

-- Messages + Photos
('messages-photos', 'messages', TRUE),
('messages-photos', 'photos', TRUE),

-- Complete Backup (all services)
('complete-backup', 'contacts', TRUE),
('complete-backup', 'messages', TRUE),
('complete-backup', 'photos', TRUE)
ON CONFLICT (plan_id, service_type_id) DO UPDATE SET
    is_included = EXCLUDED.is_included;

-- ============================================================================
-- STEP 4: Create legacy tier mapping for backward compatibility
-- ============================================================================

-- Create a mapping table for legacy subscription tiers
CREATE TABLE IF NOT EXISTS public.legacy_tier_mapping (
    legacy_tier VARCHAR(50) PRIMARY KEY,
    equivalent_plan_id VARCHAR(50) NOT NULL,
    migration_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (equivalent_plan_id) REFERENCES public.subscription_plans(id)
);

-- Insert legacy tier mappings
INSERT INTO public.legacy_tier_mapping (legacy_tier, equivalent_plan_id, migration_notes) VALUES
('basic', 'contacts-only', 'Basic tier users get contacts backup to maintain service continuity'),
('premium', 'complete-backup', 'Premium tier users get all services as they had comprehensive backup'),
('family', 'complete-backup', 'Family tier users get all services with shared storage')
ON CONFLICT (legacy_tier) DO UPDATE SET
    equivalent_plan_id = EXCLUDED.equivalent_plan_id,
    migration_notes = EXCLUDED.migration_notes;

-- ============================================================================
-- STEP 5: Create pricing calculation views for easy querying
-- ============================================================================

-- View to calculate optimal pricing for service combinations
CREATE OR REPLACE VIEW public.service_combination_pricing AS
SELECT 
    services.service_ids,
    services.service_names,
    services.individual_total_cents,
    services.total_storage_gb,
    COALESCE(sp.price_cents, services.individual_total_cents) as best_price_cents,
    COALESCE(sp.id, 'custom') as recommended_plan_id,
    COALESCE(sp.name, 'Custom Selection') as recommended_plan_name,
    CASE 
        WHEN sp.price_cents IS NOT NULL AND sp.price_cents < services.individual_total_cents 
        THEN services.individual_total_cents - sp.price_cents 
        ELSE 0 
    END as savings_cents,
    CASE 
        WHEN sp.price_cents IS NOT NULL AND sp.price_cents < services.individual_total_cents 
        THEN ROUND(((services.individual_total_cents - sp.price_cents)::DECIMAL / services.individual_total_cents) * 100, 1)
        ELSE 0 
    END as savings_percentage
FROM (
    -- Generate all possible service combinations
    SELECT 
        array_agg(st.id ORDER BY st.sort_order) as service_ids,
        string_agg(st.name, ' + ' ORDER BY st.sort_order) as service_names,
        sum(st.base_price_cents) as individual_total_cents,
        sum(st.storage_limit_gb) as total_storage_gb
    FROM public.service_types st
    WHERE st.is_active = TRUE
    GROUP BY ROLLUP(st.id)
    HAVING array_agg(st.id) IS NOT NULL
) services
LEFT JOIN public.subscription_plans sp ON (
    sp.is_active = TRUE AND
    (
        SELECT array_agg(ps.service_type_id ORDER BY ps.service_type_id)
        FROM public.plan_services ps 
        WHERE ps.plan_id = sp.id AND ps.is_included = TRUE
    ) = (
        SELECT array_agg(unnest ORDER BY unnest) 
        FROM unnest(services.service_ids)
    )
);

-- View for user subscription summary
CREATE OR REPLACE VIEW public.user_subscription_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.display_name,
    us.id as subscription_id,
    us.status as subscription_status,
    us.plan_id,
    sp.name as plan_name,
    us.total_price_cents,
    us.current_period_start,
    us.current_period_end,
    us.service_combination,
    array_agg(uss.service_type_id ORDER BY st.sort_order) FILTER (WHERE uss.is_active = TRUE) as active_services,
    string_agg(st.name, ', ' ORDER BY st.sort_order) FILTER (WHERE uss.is_active = TRUE) as active_service_names,
    sum(st.storage_limit_gb) FILTER (WHERE uss.is_active = TRUE) as total_storage_gb,
    su.total_size_bytes,
    su.contacts_count,
    su.messages_count,
    su.photos_count
FROM public.users u
LEFT JOIN public.user_subscriptions us ON u.id = us.user_id
LEFT JOIN public.subscription_plans sp ON us.plan_id = sp.id
LEFT JOIN public.user_service_selections uss ON u.id = uss.user_id
LEFT JOIN public.service_types st ON uss.service_type_id = st.id
LEFT JOIN public.storage_usage su ON u.id = su.user_id
GROUP BY 
    u.id, u.email, u.display_name, us.id, us.status, us.plan_id, 
    sp.name, us.total_price_cents, us.current_period_start, 
    us.current_period_end, us.service_combination, su.total_size_bytes,
    su.contacts_count, su.messages_count, su.photos_count;

-- ============================================================================
-- STEP 6: Add table comments and documentation
-- ============================================================================

COMMENT ON TABLE public.legacy_tier_mapping IS 'Maps old subscription tiers to new modular plans for backward compatibility';
COMMENT ON VIEW public.service_combination_pricing IS 'Calculates optimal pricing for any combination of services';
COMMENT ON VIEW public.user_subscription_summary IS 'Comprehensive view of user subscriptions with service details and usage';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Modular pricing data population completed successfully!';
    RAISE NOTICE 'Created % service types', (SELECT COUNT(*) FROM public.service_types);
    RAISE NOTICE 'Created % subscription plans', (SELECT COUNT(*) FROM public.subscription_plans);
    RAISE NOTICE 'Created % plan-service relationships', (SELECT COUNT(*) FROM public.plan_services);
    RAISE NOTICE 'Ready for user migration script (006_migrate_existing_users.sql)';
END $$;
