import { Router } from 'express';
import { SubscriptionController } from '../controllers/SubscriptionController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const subscriptionController = new SubscriptionController();

/**
 * Subscription routes for modular pricing system
 * All routes require authentication
 */

// Pricing calculation routes
// POST /api/pricing/calculate - Calculate optimal pricing for service combination
router.post('/pricing/calculate', authenticateToken, (req, res) => {
  subscriptionController.calculatePricing(req, res);
});

// GET /api/pricing/bulk - Calculate pricing for multiple service combinations
router.get('/pricing/bulk', authenticateToken, (req, res) => {
  subscriptionController.calculateBulkPricing(req, res);
});

// GET /api/pricing/services - Get individual service pricing
router.get('/pricing/services', authenticateToken, (req, res) => {
  subscriptionController.getServicePricing(req, res);
});

// Subscription management routes
// POST /api/subscriptions - Creates new subscription with service combination
router.post('/', authenticateToken, (req, res) => {
  subscriptionController.createSubscription(req, res);
});

// PUT /api/subscriptions/:subscriptionId - Updates existing subscription services
router.put('/:subscriptionId', authenticateToken, (req, res) => {
  subscriptionController.updateSubscription(req, res);
});

// GET /api/subscriptions/:userId - Returns user's current subscription details
router.get('/:userId', authenticateToken, (req, res) => {
  subscriptionController.getSubscriptionDetails(req, res);
});

// DELETE /api/subscriptions/:subscriptionId - Cancels subscription
router.delete('/:subscriptionId', authenticateToken, (req, res) => {
  subscriptionController.cancelSubscription(req, res);
});

// Service access validation routes
// GET /api/subscriptions/:userId/access/:serviceId - Check if user has access to specific service
router.get('/:userId/access/:serviceId', authenticateToken, (req, res) => {
  subscriptionController.checkServiceAccess(req, res);
});

// GET /api/subscriptions/:userId/services - Get all services user has access to
router.get('/:userId/services', authenticateToken, (req, res) => {
  subscriptionController.getUserServices(req, res);
});

export default router;